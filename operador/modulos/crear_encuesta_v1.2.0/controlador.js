const URL_MODULO = 'modulos/crear_encuesta_v1.2.0';

let dtable;
let html;
let html2;
let asignacion = {
    etapa1: {
        operador: {},
        distribuidor: {
            vendedor: false,
            supervisor: false
        },
        sistema: false
    },
    etapa2: {
        operador: {},
        distribuidor: {
            usuario_sistema: false,
            vendedor: false,
            supervisor: false
        },
        pdv: false
    }
};

let tipo_crud = 0;
let id_enc = 0;
let vigencia_enc = 0;
let cant_pre = 0;
let cant_resp = 0;
let estado_en = 1;

let val_vigencia = 0;
let perm = 0;
let in_regional = [];
let in_dis = [];
let in_ter = [];
let in_zonas = [];

let datos_reg = [];
let datos_dis = [];
let datos_ter = [];
let datos_zonas = [];

let d = new Date();
let dia_act = d.getDate();
let mes_act = d.getMonth() + 1;
let anio = d.getFullYear();
let fecha_i = new Date();
let fecha_f = new Date();
let id_pre = 0;
let tipo_crud_pre = 0;
let tipo_crud_resp = 0;
let id_resp = 0;
let tr_pre = "";
let row_pre = "";
let id_btn_pre = "";
let tipo_pregunta = 0;
let arrayRama = [];
let id_encuesta_origen = 0;
let id_pre_origen = 0;
let id_resp_origen = 0;


/**
 * Valida si el parámetro enviado es un JSON
 * @return {boolean}
 */
function checkJSON(data) {
    try {
        if (data !== true)
            JSON.parse(JSON.stringify(data));
        else
            return false;
    } catch (error) {
        return false;
    }

    return true;
}

function fechas() {
    $("#fecha_ini_b, #fecha_fin_b").parent().datepicker({
        format: "yyyy-mm-dd"
    });

    $("#fecha_ini, #fecha_fin").parent().datepicker({
        format: "yyyy-mm-dd",
        startDate: "0"
    });

    $("#fecha_ini").on("change", () => {
        let startDateEndA = $("#fecha_ini").val() || "0";

        $("#fecha_fin").parent().datepicker("setStartDate", startDateEndA);
    });

    /*$("#fecha_fin").on("change", () => {
      let startDateEndA = $("#fecha_fin").val() || "0";

      $("#fecha_ini").parent().datepicker("setStartDate", startDateEndA);
    });*/
}

function pasos() {
    /* paso 1 */
    $("#siguiente-paso1").click(() => {
        let estado_paso_actual = false;

        // Oculta las asignaciones del paso 2
        $("#asig-paso2-distri, #asig-paso2-oper, #asig-paso2-sis").hide();

        for (let i in asignacion.etapa1.distribuidor) {
            if (asignacion.etapa1.distribuidor[i]) {
                $("#asig-paso2-distri").show();

                break;
            }
        }

        for (let i in asignacion.etapa1.operador) {
            if (asignacion.etapa1.operador[i]) {
                $("#asig-paso2-oper").show();

                break;
            }
        }

        if (asignacion.etapa1.sistema)
            $("#asig-paso2-sis").show();

        /**
         * Recorre la etapa 1 de la asignación, validando
         * si tiene alguna asignación
         */
        for (let i in asignacion.etapa1) {
            if (asignacion.etapa1[i] !== true && asignacion.etapa1[i] !== false && checkJSON(asignacion.etapa1[i])) {
                for (let ii in asignacion.etapa1[i]) {
                    if (asignacion.etapa1[i][ii])
                        estado_paso_actual = true;
                }
            } else {
                if (asignacion.etapa1[i])
                    estado_paso_actual = true;
            }
        }

        /**
         * Valida si hay alguna asignación
         */
        if (estado_paso_actual) {
            $("#paso1, #paso1l").removeClass("activo").addClass("desactivo");
            $("#paso2l, #paso3").removeClass("desactivo").addClass("activo");
            $("#contenedor-paso1").hide();
            $("#contenedor-paso2").show();
        } else {
            Notificacion("Debes realizar alguna asignación para pasar al siguiente paso", "warning");
        }
    });

    /* paso 2 */
    $("#siguiente-paso2").click(() => {
        let estado_paso_actual = false;

        /**
         * Recorre la etapa 2 de la asignación, validando
         * si tiene alguna asignación
         */
        for (let i in asignacion.etapa2) {
            if (checkJSON(asignacion.etapa2[i])) {
                for (let ii in asignacion.etapa2[i]) {
                    if (asignacion.etapa2[i][ii])
                        estado_paso_actual = true;
                }
            } else {
                if (asignacion.etapa2[i])
                    estado_paso_actual = true;
            }
        }

        // Valida si no hay asignaciones en la etapa 2
        if (!estado_paso_actual) {
            /**
             * Recorre la etapa 1 de la asignación, validando
             * si tiene alguna asignación (distribuidor)
             */
            for (let i in asignacion.etapa1.distribuidor) {
                if (asignacion.etapa1.distribuidor[i])
                    estado_paso_actual = true;
            }

            /**
             * Recorre la etapa 1 de la asignación, validando
             * si tiene alguna asignación (operador)
             */
            for (let i in asignacion.etapa1.operador) {
                if (asignacion.etapa1.operador[i])
                    estado_paso_actual = true;
            }
        }

        /**
         * Valida si hay alguna asignación
         */
        if (estado_paso_actual) {
            $("#paso2, #paso2l").removeClass("activo").addClass("desactivo");
            $("#paso3, #paso3l, #paso4").removeClass("desactivo").addClass("activo");
            $("#contenedor-paso2").hide();
            $("#contenedor-paso3").show();
        } else {
            Notificacion("Debes realizar alguna asignación para pasar al siguiente paso", "warning");
        }
    });

    $("#atras-paso2").click(() => {
        $("#paso2l, #paso3").removeClass("activo").addClass("desactivo");
        $("#paso1, #paso1l").removeClass("desactivo").addClass("activo");
        $("#contenedor-paso1").show();
        $("#contenedor-paso2").hide();
        $("#asig-paso2-distri, #asig-paso2-oper, #asig-paso2-sis").hide();
    });

    /* paso 3 */
    $("#atras-paso3").click(() => {
        $("#paso3l, #paso4").removeClass("activo").addClass("desactivo");
        $("#paso2, #paso2l").removeClass("desactivo").addClass("activo");
        $("#contenedor-paso2").show();
        $("#contenedor-paso3").hide();
    });
}

function perfiles_operador() {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "perfiles_operador"
        })
    }).then(response => response.json()).then(data => {
        html = "";
        html2 = "";

        $.each(data, (index, row) => {
            /**
             * Guarda el ID con el que se identifica el perfil
             */
            asignacion.etapa1.operador[`item_oper_app_${row.id}`] = false;
            asignacion.etapa2.operador[`item_oper_paso2_${row.id}`] = false;

            /**
             * Crea el HTML para el listado de los perfiles del operador (aplicación)
             */
            html += "<div>";
            html += `  <i id="item_oper_app_${row.id}" class="glyphicon glyphicon-unchecked check_oper_app"></i>`;
            html += `  <span>${row.nombre}</span>`;
            html += "</div>";

            /**
             * Crea el HTML para el listado de los perfiles del operador (sistema/web)
             */
            html2 += "<div>";
            html2 += `  <i id="item_oper_paso2_${row.id}" class="glyphicon glyphicon-unchecked check_oper_paso2"></i>`;
            html2 += `  <span>${row.nombre}</span>`;
            html2 += "</div>";
        });

        $("#deta_asig_oper_app").html(html);
        $("#det-paso2-sis-op").html(html2);
    }).catch(error => {
        console.error('Error:', error);
    });
}

function cargar_titulos(option) {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "ver_titulos",
            op: option
        })
    }).then(response => response.json()).then(data => {
        html = "<option value=''>Seleccionar...</option>";

        $.each(data, function (index, row) {
            html += "<option value=" + row.id_enc + " >" + row.titulo_enc + "</option>";
        });

        if (option == 0) {
            $("#titulob").html(html).change();
        } else if (option == 1) {
            $("#seleccionar_encuesta").html(html).change();
        } else {
            $("#seleccionar_encuesta_respuesta").html(html).change();
        }
    }).catch(error => {
        console.error('Error:', error);
    });
}

function cargar_preguntas(idEncuesta, idRespuesta, option = undefined, cantidad_preg = undefined) {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "ver_preguntas",
            idEncuesta: idEncuesta,
            idRespuesta: idRespuesta
        })
    }).then(response => response.json()).then(data => {
        html = "<option value=''>Seleccionar...</option>";
        $.each(data, function (index, row) {
            if (option == 0 && (row.tipo != 2 && row.tipo != 3 && row.tipo != 4) || option !== 0 && (row.tipo == 2 && cantidad_preg == 0 || row.tipo != 2 && row.tipo != 3 && row.tipo != 4))
                html += "<option value=" + row.id_pregunta + " >" + row.pregunta + "</option>";
        });

        if (option == 0) {
            $("#seleccionar_pregunta").html(html).change();
        } else {
            $("#seleccionar_pregunta_respuesta").html(html).change();
        }
    }).catch(error => {
        console.error('Error:', error);
    });
}

function cargar_respuestas(idEncuesta, idPregunta) {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "ver_respuestas",
            idEncuesta: idEncuesta,
            idPregunta: idPregunta
        })
    }).then(response => response.json()).then(data => {
        html = "<option value=''>Seleccionar...</option>";

        $.each(data, function (index, row) {
            html += "<option value=" + row.id_respuesta + " >" + row.respuesta + "</option>";
        });

        $("#seleccionar_respuesta").html(html).change();
    }).catch(error => {
        console.error('Error:', error);
    });
}

/**
 * Guarda la asignación de la encuesta
 * @param id_enc
 */
function guardar_asignacion(id_enc) {
    let band_check = 0;
    let msg;

    let band_perm = 0;
    let regionales = [];
    let distribuidores = [];
    let circuitos = [];
    let rutas = [];

    let val_regio = [];
    let val_distri = [];
    let val_terri = [];
    let val_zonas = [];

    /**
     * Paso 1 - Distribuidor
     */
    let pdv = 0;
    let vendedor_pdv = 0;
    let supervisor_pdv = 0;

    if ($(".app-pdv").hasClass("glyphicon-check")) {
        band_check = 1;
        pdv = 1;

        if ($(".app-vende-pdv").hasClass("glyphicon-check")) {
            vendedor_pdv = 1;
        }

        if ($(".app-super-pdv").hasClass("glyphicon-check")) {
            supervisor_pdv = 1;
        }
    }

    /**
     * Paso 2 - Distribuidor
     */
    let app = 0;
    let usuario_sistema = 0;
    let vendedor = 0;
    let supervisor = 0;

    if (asignacion.etapa1.sistema) {
        band_check = 1;
        app = 1;

        if ($(".app-usuario_sistema").hasClass("glyphicon-check")) {
            usuario_sistema = 1;
        }

        if ($(".app-vende").hasClass("glyphicon-check")) {
            vendedor = 1;
        }

        if ($(".app-super").hasClass("glyphicon-check")) {
            supervisor = 1;
        }
    }

    /**
     * Paso 2 - PDV (punto recarga)
     */
    let punto_recargar = 0;

    if (asignacion.etapa1.sistema) {
        band_check = 1;

        /*if ($(".app-vende").hasClass("glyphicon-check")) {
          punto_recargar = 1;
        }

        if ($(".app-super").hasClass("glyphicon-check")) {
          punto_recargar = 1;
        }*/

        if ($(".sis-pdv").hasClass("glyphicon-check")) {
            punto_recargar = 1;
        }
    }

    /**
     * Paso 3 - Asignación DCS
     */
    if (in_regional.length > 0) {
        $.each(in_regional, (index, fila) => {
            if (fila !== undefined) {
                if (val_regio.indexOf(fila.id_reg) == -1) {
                    regionales.push({ id_reg: fila.id_reg, op: "regio" });
                    val_regio.push(fila.id_reg);
                    band_perm = 1;
                    band_check = 1;
                }
            }
        });
    }

    if (in_dis.length > 0) {
        $.each(in_dis, (index, fila) => {
            if (fila !== undefined) {
                if (val_regio.indexOf(fila.id_reg) == -1) {
                    if (val_distri.indexOf(fila.id_dis) == -1) {
                        distribuidores.push({ id_reg: fila.id_reg, id_dis: fila.id_dis, op: "distri" });
                        val_distri.push(fila.id_dis);
                        band_perm = 1;
                        band_check = 1;
                    }
                }
            }
        });
    }

    if (in_ter.length > 0) {
        $.each(in_ter, (index, fila) => {
            if (fila !== undefined) {
                if (val_regio.indexOf(fila.id_reg) == -1) {
                    if (val_distri.indexOf(fila.id_dis) == -1) {
                        if (val_terri.indexOf(fila.id_terri) == -1) {
                            circuitos.push({ id_reg: fila.id_reg, id_dis: fila.id_dis, id_terri: fila.id_terri, op: "terri" });
                            val_terri.push(fila.id_terri);
                            band_perm = 1;
                            band_check = 1;
                        }
                    }
                }
            }
        });
    }

    if (in_zonas.length > 0) {
        $.each(in_zonas, (index, fila) => {
            if (fila !== undefined) {
                if (val_regio.indexOf(fila.id_reg) == -1) {
                    if (val_distri.indexOf(fila.id_dis) == -1) {
                        if (val_terri.indexOf(fila.id_terri) == -1) {
                            if (val_zonas.indexOf(fila.id_zona) == -1) {
                                rutas.push({
                                    id_reg: fila.id_reg,
                                    id_dis: fila.id_dis,
                                    id_terri: fila.id_terri,
                                    id_zona: fila.id_zona,
                                    op: "zona"
                                });
                                val_zonas.push(fila.id_zona);
                                band_perm = 1;
                                band_check = 1;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Valida si se realiza alguna asignación
     */
    if (band_check) {
        if (band_perm) {
            /**
             * Crea el mensaje del cuadro de dialogo
             * @type {string}
             */
            msg = "<div style=\"text-align: center;\">";
            msg += "  <p>¿Está seguro de guardar la asignación?</p>";

            if (vigencia_enc == 0)
                msg += "<br /><p><b>NOTA:</b> La encuesta estará en vigencia a partir de que se asignen los permisos. Se debe tener en cuenta que no se puede editar una encuesta mientras esté vigente.</p>";

            msg += "</div>";

            /**
             * Cuadro de dialogo
             */
            BootstrapDialog.confirm(msg, function (result) {
                if (result) {
                    $(".modal").modal("hide");
                    $("#modal_info").modal("show");

                    let regionales_c = toHex(JSONC.pack(regionales));
                    let distribuidores_c = toHex(JSONC.pack(distribuidores));
                    let circuitos_c = toHex(JSONC.pack(circuitos));
                    let rutas_c = toHex(JSONC.pack(rutas));
                    let operador_sistema = (asignacion.etapa1.sistema) ? asignacion.etapa2.operador : {};

                    fetch(`${URL_MODULO}/controlador.php`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            accion: "guardar_asignacion",
                            id_enc: id_enc,

                            // Paso 1: operador
                            operador_app: encodeURI(JSON.stringify(asignacion.etapa1.operador)),

                            // Paso 1: distribuidor
                            pdv,
                            vendedor_pdv,
                            supervisor_pdv,

                            // Paso 2: operador
                            operador_sistema: encodeURI(JSON.stringify(operador_sistema)),

                            // Paso 2: distribuidor
                            app,
                            vendedor,
                            supervisor,
                            usuario_sistema,

                            // Paso 2: PDV (punto recarga)
                            punto_recargar,

                            // Paso 3: asignación DCS
                            in_regional: regionales_c,
                            in_dis: distribuidores_c,
                            in_ter: circuitos_c,
                            in_zonas: rutas_c
                        })
                    }).then(response => response.json()).then(data => {
                        if (data["estado"] == 1) {
                            Notificacion(data["msg"], "success");

                            Cargar_Tabla();
                        } else if (data["estado"] == 0) {
                            Notificacion(data["msg"], "error");
                        }

                        $(".modal").modal("hide");
                    }).catch(error => {
                        console.error('Error:', error);
                    });
                }
            });
        } else {
            Notificacion("Es necesario que realice la asignación de algún permiso DCS", "warning");
        }
    } else {
        Notificacion("Es necesario que realice alguna asignación para guardar los cambios", "warning");
    }
}

$(document).ready(() => {
    fechas();
    pasos();
    perfiles_operador();
    cargar_titulos(0);

    // Botón crear encuesta
    $("#btn_crear").click(function () {
        limpiar_form(1);

        tipo_crud = 1;
        id_enc = 0;
        vigencia_enc = 0;
        cant_pre = 0;
        cant_resp = 0;

        $("#titulo_noti_promo").html("Crear Encuesta");
        $("#btn_accion").html("Crear");
        $("#modal_crud_encuesta").modal("show");
    });

    // Formulario para buscar
    $("#frm_b_noti").submit(function (event) {
        event.preventDefault();
        Cargar_Tabla();
    });

    // Formulario de crear encuesta
    $("#frm_crud").submit(function (event) {
        event.preventDefault();
        crud_encuesta();
    });

    // Formulario crear preguntas
    $("#frm_crud_preguntas").submit(function (event) {
        event.preventDefault();
        crud_preguntas();
    });

    // Formulario crear respuestas
    $("#frm_crud_respuestas").submit(function (event) {
        event.preventDefault();
        crud_respuestas();
    });

    // Reporte de encuestas
    $("#reporte-encuestas").click(() => {
        window.open("?mod=reporte_encuesta_dcs_v1.0.0", "_blank");
    });
});

function Cargar_Tabla() {
    let fecha_ini_b = $("#fecha_ini_b").val();
    let fecha_fin_b = $("#fecha_fin_b").val();
    let titulob = $("#titulob").val();
    let estadob = $("#estadob").val();

    if (!$.fn.DataTable.isDataTable("#tbl")) {
        dtable = $("#tbl").DataTable({
            ajax: function (data, callback, settings) {
                fetch(`${URL_MODULO}/controlador.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        accion: "Consultar",
                        fecha_ini_b: fecha_ini_b,
                        fecha_fin_b: fecha_fin_b,
                        titulob: titulob,
                        estadob: estadob
                    })
                }).then(response => response.json()).then(result => {
                    callback(result);
                }).catch(error => {
                    console.error('Error:', error);
                });
            },
            "bFilter": true,
            "responsive": true,
            "language": {
                "emptyTable": "Ningún dato",
                "infoEmpty": "No se ha encontrado ningún resultado",
                "search": "_INPUT_",
                "searchPlaceholder": "Buscar..."
            },
            "columns": [
                { "data": "titulo_enc" },
                { "data": "titulo_enc" },
                { "data": "des_enc" },
                { "data": "user_crea" },
                { "data": "cant_pre" },
                { "data": "fecha_ini" },
                { "data": "fecha_fin" },
                { "data": "obliga" },
                { "data": "navega" },
                { "data": "estado" },
                { "data": "estado" },
                { "data": "vigente" },
                { "data": "estado" },
                { "data": "estado" },
                { "data": "estado" },
                { "data": "estado" },
                { "data": "estado" }
            ],
            "columnDefs": [{
                "targets": 0,
                "data": "titulo_enc",
                render: (data, type, row) => {
                    let color = "#00638d";
                    let classBtn = "btnDesplegarPreguntas";

                    return "<button type=\"button\" name=\"btnDesplegarPreguntas\" id=\"btnDesplegarPreguntas\" class=\"btn " + classBtn + "\" data-id_enc=\"" + row.id_enc + "\" data-vigente=\"" + row.vigente + "\" style=\"border: 1px solid " + color + ";color: " + color + "; margin-top: 3px;\"><span class=\"glyphicon glyphicon-plus desplegar\" aria-hidden=\"true\" style=\"font-size: 12px;\"></span></button>";
                }
            },
            {
                "targets": 1,
                "data": "titulo_enc",
                render: (data) => {
                    let des = "";

                    for (let i in data) {
                        if (data[i] !== "" && i <= 200) {
                            des += data[i];

                            if (i == 200)
                                des += "...";
                        }
                    }

                    return des;
                }
            },
            {
                "targets": 2,
                "data": "des_enc",
                render: (data) => {
                    let des = "";

                    for (let i in data) {
                        if (data[i] !== "" && i <= 150) {
                            des += data[i];

                            if (i == 150)
                                des += "...";
                        }
                    }

                    return des;
                }
            },
            {
                "targets": 9,
                "data": "estado",
                render: (data, type, row) => {
                    if (data == "1") {
                        return "<i class=\"glyphicon glyphicon-ok-circle estado\" title=\"Activo\" style=\"cursor:pointer; font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\" data-vigente=\"" + row.vigente + "\"></i>";
                    }

                    if (data == "0") {
                        return "<i class=\"glyphicon glyphicon-ban-circle estado\" title=\"Inactivo\" style=\"cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\" data-vigente=\"" + row.vigente + "\"></i>";
                    }
                }
            },
            {
                "targets": 10,
                "data": "estado",
                render: function (data, type, row) {
                    return "<i class=\"glyphicon glyphicon-remove-sign eliminar\" title=\"Eliminar\" style=\"cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\" data-vigente=\"" + row.vigente + "\"></i>";
                }
            },
            {
                "targets": 11,
                "data": "vigente",
                render: (data, type, row) => {
                    let date = new Date();
                    let day = (date.getDate() < 10) ? `0${date.getDate()}` : date.getDate();
                    let month = ((date.getMonth() + 1) < 10) ? `0${(date.getMonth() + 1)}` : (date.getMonth() + 1);
                    let year = date.getFullYear();

                    let current_date = new Date(`${year}-${month}-${day}`);
                    let fecha_fin = new Date(row.fecha_fin);

                    if (data == "1" && fecha_fin >= current_date) {
                        return "<i class=\"glyphicon glyphicon-ok-circle\" title=\"Activo\" style=\"font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\"></i>";
                    } else {
                        return "<i class=\"glyphicon glyphicon-ban-circle\" title=\"Inactivo\" style=\"font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\"></i>";
                    }
                }
            },
            {
                "targets": 13,
                "data": "",
                render: function (data, type, row) {
                    return "<i class=\"glyphicon glyphicon-duplicate duplicarEncuesta\" title=\"Duplicar\" style=\"font-size: 25px; color: #10628a; margin-top: 7px;cursor:pointer;\" data-id_enc=\"" + row.id_enc + "\" data-permisos=\"" + row.permisos + "\" data-obliga=\"" + row.obligatorio + "\" data-navega=\"" + row.navegar_atras + "\" data-vigente=\"" + row.vigente + "\"></i>";
                }
            },
            {
                "targets": 12,
                "data": "",
                render: function (data, type, row) {
                    if (row.reporte_distri == 1) {
                        return "<i class=\"glyphicon glyphicon-save-file reporteDistri\" title=\"Activo\" style=\"font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\" data-reporte_distri=\"" + row.reporte_distri + "\" ></i>";
                    } else {
                        return "<i class=\"glyphicon glyphicon-save-file reporteDistri\" title=\"Inactivo\" style=\"font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_enc=\"" + row.id_enc + "\" data-reporte_distri=\"" + row.reporte_distri + "\" ></i>";
                    }
                }
            },
            {
                "targets": 14,
                "data": "",
                render: function (data, type, row) {
                    return "<i class=\"glyphicon glyphicon-plus-sign s_presguntas\" title=\"Agregar Pregunta\" style=\"font-size: 25px; color: #10628a; margin-top: 8px;cursor:pointer;\" data-id_enc=\"" + row.id_enc + "\" data-titulo=\"" + row.titulo_enc + "\" data-vigente=\"" + row.vigente + "\"></i>";
                }
            },
            {
                "targets": 15,
                "data": "",
                render: function (data, type, row) {
                    return "<button class=\"btn btn-sm btn-primary editar\" style=\"margin-top: 6px;\"  data-reporte=\"" + row.reporte_distri + "\" data-aplicar_encuesta=\"" + row.aplicar + "\"  data-estado=\"" + row.estado + "\" data-fecha_ini=\"" + row.fecha_ini + "\" data-fecha_fin=\"" + row.fecha_fin + "\" data-id_enc=\"" + row.id_enc + "\" data-permisos=\"" + row.permisos + "\" data-obliga=\"" + row.obligatorio + "\" data-navega=\"" + row.navegar_atras + "\" data-vigente=\"" + row.vigente + "\"><i class=\"fa fa-edit\" title=\"Editar\"></i></button>";
                }
            },
            {
                "targets": 16,
                "data": "",
                render: function (data, type, row) {
                    return "<button class=\"btn btn-sm btn-primary asignar\" style=\"margin-top: 6px;\" data-titulo=\"" + row.titulo_enc + "\" data-id_enc=\"" + row.id_enc + "\" data-vigente=\"" + row.val_vigencia + "\" data-estado=\"" + row.estado + "\" ><i class=\"glyphicon glyphicon-new-window\" title=\"Editar\"></i></button>";
                }
            }
            ],

            fnDrawCallback: () => {
                $(".duplicarEncuesta").unbind("click");
                $(".duplicarEncuesta").click(function () {
                    limpiar_form(1);

                    let data = dtable.row($(this).parents("tr")).data();
                    let fecha_ini = $(this).attr("data-fecha_ini");
                    let fecha_fin = $(this).attr("data-fecha_fin");
                    let obliga = $(this).attr("data-obliga");
                    let navega = $(this).attr("data-navega");

                    id_enc = $(this).attr("data-id_enc");
                    vigencia_enc = 0;
                    tipo_crud = 3;

                    $("#titulo").val(data["titulo_enc"]);
                    $("#descrip_e").val(data["des_enc"]);
                    $("#fecha_ini").val(fecha_ini);
                    $("#fecha_fin").val(fecha_fin);

                    $("input:radio[name=c_obliga][value='" + obliga + "']").prop("checked", true);
                    $("input:radio[name=c_navega][value='" + navega + "']").prop("checked", true);

                    $("#titulo_noti_promo").html("Duplicar Encuesta");
                    $("#btn_accion").html("Duplicar");
                    $("#modal_crud_encuesta").modal("show");
                });

                $(".btnDesplegarPreguntas").unbind("click");
                $(".btnDesplegarPreguntas").click(function () {
                    let tr = $(this).closest("tr");
                    let row = dtable.row(tr);
                    let id_encuesta = $(this).attr("data-id_enc");

                    vigencia_enc = $(this).attr("data-vigente");

                    crear_vista_preguntas(id_encuesta, 0, 0, tr, row, this, vigencia_enc, 0, 0);
                });

                $(".editar").unbind("click");
                $(".editar").click(function () {
                    let data = dtable.row($(this).parents("tr")).data();

                    var disabled = false;

                    if ($(this).attr("data-vigente") == 1) {
                        disabled = true;
                    }

                    val_vigencia = $(this).attr("data-vigente");
                    estado_en = $(this).attr("data-estado");
                    perm = $(this).attr("data-permisos");
                    id_enc = $(this).attr("data-id_enc");
                    vigencia_enc = 0;
                    tipo_crud = 2;
                    fecha_i = $(this).attr("data-fecha_ini");
                    fecha_f = $(this).attr("data-fecha_fin");

                    let fecha_ini = $(this).attr("data-fecha_ini");
                    let fecha_fin = $(this).attr("data-fecha_fin");
                    let obliga = $(this).attr("data-obliga");
                    let navega = $(this).attr("data-navega");
                    let aplicar_encuesta = $(this).attr("data-aplicar_encuesta");
                    let reporte = $(this).attr("data-reporte");

                    $("#titulo").val(data["titulo_enc"]);
                    $("#descrip_e").val(data["des_enc"]);
                    $("#fecha_ini").val(fecha_ini);
                    $("#fecha_fin").val(fecha_fin);

                    $("input:radio[name=c_obliga][value='" + obliga + "']").prop("checked", true);
                    $("input:radio[name=c_navega][value='" + navega + "']").prop("checked", true);

                    $("input:radio[name=aplicar_encuesta][value='" + aplicar_encuesta + "']").prop("checked", true);
                    $("input:radio[name=reporte_distri][value='" + reporte + "']").prop("checked", true);


                    $("#titulo_noti_promo").html("Editar Encuesta");
                    $("#modal_crud_encuesta").modal("show");
                    $("#btn_accion").html("Editar");

                    $("#titulo").prop("disabled", disabled);
                    $("#descrip_e").prop("disabled", disabled);
                    $("#fecha_ini").prop("disabled", disabled);
                    $("input:radio[name=c_obliga]").prop("disabled", disabled);
                    $("input:radio[name=c_navega]").prop("disabled", disabled);
                    $("input:radio[name=aplicar_encuesta]").prop("disabled", disabled);
                    $("input:radio[name=reporte_distri]").prop("disabled", disabled);


                    /* } else {
                         BootstrapDialog.show({
                             title: data["titulo_enc"],
                             closable: true,
                             closeByBackdrop: false,
                             closeByKeyboard: false,
                             size: BootstrapDialog.SIZE_WIDE,
                             message: "<div style=\"text-align: center;\">No es posible editar la encuesta mientras esté vigente</div>",
                             buttons: [{
                                 label: "Cerrar",
                                 cssClass: "btn",
                                 action: function(dialog) {
                                     dialog.close();
                                 }
                             }]
                         });
                     }*/
                });

                $(".s_presguntas").unbind("click");
                $(".s_presguntas").click(function () {
                    id_enc = $(this).attr("data-id_enc");
                    vigencia_enc = $(this).attr("data-vigente");

                    modal_preguntas(id_enc, vigencia_enc, 0, 0, "", 0, 0, 0, 0, 1);
                });

                $(".estado").unbind("click");
                $(".estado").click(function () {
                    let element = this;
                    let data = dtable.row($(element).parents("tr")).data();
                    let estado = data["estado"];
                    let msg = "";

                    id_enc = $(this).attr("data-id_enc");

                    if ($(this).attr("data-vigente") == 1 && estado == 1) {
                        msg = "<br><br><b>NOTA:</b> Al inactivar esta encuesta, se debe tener en cuenta que los permisos DCS serán eliminados";
                    }

                    BootstrapDialog.confirm("¿Está seguro de cambiar el estado de la encuesta?" + msg, function (result) {
                        if (result) {
                            fetch(`${URL_MODULO}/controlador.php`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    accion: "cambiar_estado",
                                    id_enc: id_enc,
                                    estado: estado
                                })
                            }).then(response => response.json()).then(data => {
                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");
                                    Cargar_Tabla();
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                            }).catch(error => {
                                console.error('Error:', error);
                            });
                        }
                    });
                });

                $(".reporteDistri").unbind("click");
                $(".reporteDistri").click(function () {
                    let element = this;
                    let data = dtable.row($(element).parents("tr")).data();

                    reporte_distri = $(this).attr("data-reporte_distri");
                    id_enc = $(this).attr("data-id_enc");


                    BootstrapDialog.confirm("¿Está seguro de realizar este cambio en la encuesta?", function (result) {
                        if (result) {
                            fetch(`${URL_MODULO}/controlador.php`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    accion: "reporte_distri",
                                    id_enc: id_enc,
                                    reporte_distri: reporte_distri
                                })
                            }).then(response => response.json()).then(data => {
                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");
                                    Cargar_Tabla();
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                            }).catch(error => {
                                console.error('Error:', error);
                            });
                        }
                    });
                });

                $(".eliminar").unbind("click");
                $(".eliminar").click(function () {
                    id_enc = $(this).attr("data-id_enc");

                    BootstrapDialog.confirm("¿Está seguro de <b style='color:red'>ELIMINAR</b> la encuesta?", function (result) {
                        if (result) {
                            fetch(`${URL_MODULO}/controlador.php`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    accion: "eliminar_encuesta",
                                    id_enc: id_enc
                                })
                            }).then(response => response.json()).then(data => {
                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");
                                    Cargar_Tabla();
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                            }).catch(error => {
                                console.error('Error:', error);
                            });
                        }
                    });
                });

                $(".asignar").unbind("click");
                $(".asignar").click(function () {
                    /**
                     * Pone como debe estar por defecto el contenedor
                     * de la asignación (inicio)
                     */
                    $("#contenedor-paso1").show();
                    $("#contenedor-paso2, #contenedor-paso3").hide();

                    $("#paso2l, #paso3, #paso3l, #paso4").removeClass("activo").addClass("desactivo");
                    $("#paso1, #paso1l, #paso2").removeClass("desactivo").addClass("activo");
                    /**
                     * Pone como debe estar por defecto el contenedor
                     * de la asignación (fin)
                     */

                    /**
                     * Restaura los check (inicio)
                     */
                    $("i.glyphicon-check").not(".disabled").removeClass("glyphicon-check").addClass("glyphicon-unchecked");
                    /**
                     * Restaura los check (fin)
                     */

                    let titulo = $(this).attr("data-titulo");
                    let estado = $(this).attr("data-estado");

                    id_enc = $(this).attr("data-id_enc");
                    vigencia_enc = $(this).attr("data-vigente");

                    if (estado == 0) {
                        BootstrapDialog.show({
                            title: titulo,
                            closable: true,
                            closeByBackdrop: false,
                            closeByKeyboard: false,
                            size: BootstrapDialog.SIZE_WIDE,
                            message: "<div style=\"text-align: center;\">La encuesta se encuentra inactiva, no es posible asignar permisos DCS</div>",
                            buttons: [{
                                label: "Cerrar",
                                cssClass: "btn",

                                action: function (dialog) {
                                    dialog.close();
                                }
                            }]
                        });
                    } else {
                        fetch(`${URL_MODULO}/controlador.php`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                accion: "validar_encuesta",
                                id_enc: id_enc
                            })
                        }).then(response => response.text()).then(data => {
                            if (data == 1) {
                                $("#titulo_asig").html(titulo);
                                $("#modal_asig").modal();

                                cargar_asignacion(id_enc);
                            } else if (data == 0) {
                                BootstrapDialog.show({
                                    title: titulo,
                                    closable: true,
                                    closeByBackdrop: false,
                                    closeByKeyboard: false,
                                    size: BootstrapDialog.SIZE_WIDE,
                                    message: "<div style=\"text-align: center;\">La encuesta se encuentra sin preguntas o sin respuestas, no es posible asignar permisos DCS</div>",
                                    buttons: [{
                                        label: "Cerrar",
                                        cssClass: "btn",
                                        action: function (dialog) {
                                            dialog.close();
                                        }
                                    }]
                                });
                            }
                        }).catch(error => {
                            console.error('Error:', error);
                        });
                    }
                });
            }
        });

        $("#consulta").show();
    } else {
        dtable.destroy();

        Cargar_Tabla();
    }

    // Campo de busqueda en la tabla
    $("#tbl_filter").parent().addClass("filter-width");
}

function crear_vista_preguntas(id_encuesta, id_respuesta, id_pregunta, tr, row, btn, vigencia, op) {
    if ($(btn).children().hasClass("desplegar") || (!$(btn).children().hasClass("desplegar") && op == 1)) {
        $(btn).children().removeClass("glyphicon glyphicon-plus desplegar");
        $(btn).children().addClass("glyphicon glyphicon-minus noDesplegar");

        fetch(`modulos/crear_preguntas_v1.0.0/controlador.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: "ver_respuestas",
                id_enc: id_encuesta,
                id_respuesta: id_respuesta
            })
        }).then(response => response.json()).then(data => {
            data = JSON.parse(data);
            html = "";

            let margin = (id_respuesta > 0) ? "6px" : "19px";
            let class_desplegar = "glyphicon glyphicon-plus desplegar";
            let color = "#00638d";
            let classBtn = "btnDesplegarRespuestas";
            let padding = (id_respuesta > 0) ? "46px" : "61px";

            html = "<table style=\"width: calc(100% - " + margin + "); border-left: 4px solid #00638d; margin-left: " + margin + ";\" id = \"encuesta_" + id_encuesta + "_" + id_respuesta + "\">";
            html += "<thead>";
            html += "<tr>";
            html += "<th style=\"width: 4%;\"></th>";
            html += "<th></th>";
            html += "<th>PREGUNTAS</th>";
            html += "<th>TIPO PREGUNTA</th>";
            html += "<th>OBLIGATORIA</th>";
            html += "<th>CANTIDAD RESPUESTAS</th>";
            html += "<th>ESTADO</th>";
            html += "<th>ELIMINAR</th>";
            html += "<th>DUPLICAR</th>";
            html += "<th>EDITAR</th>";
            html += "<th>AGREGAR RESPUESTA</th>";
            html += "</tr>";
            html += "</thead>";
            html += "<tbody class=\"moverPreguntas\">";

            $.each(data, function (index, row) {
                html += "<tr style=\"cursor:move;\" id=\"mover_" + row.id_pregunta + "_" + id_respuesta + "\" data-id_pre=\"" + row.id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\" class=\"moverPre\">";
                html += "<td style=\"text-align: right;\"><div style=\"background-color: #00638d;width: 12px;height: 12px;border-radius: 50%;position: relative;right: 8px;\"></div></td>";
                html += "<td style=\"background-color: #fff; text-align: left;padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;border-left: 1px solid #006591;\"><button type=\"button\" class=\"btn " + classBtn + "\" data-id_pre=\"" + row.id_pregunta + "\" id = \"btn_" + row.id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-tipo=\"" + row.tipo + "\" style=\"border: 1px solid " + color + ";color: " + color + ";\"><span class=\"" + class_desplegar + "\" aria-hidden=\"true\" style=\"font-size: 12px;\"></span></button></td>";
                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">" + row.pregunta_enc + "</td>";

                if (row.tipo == 1) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">Única</td>";
                } else if (row.tipo == 2) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">Múltiple</td>";
                } else if (row.tipo == 3) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">Abierta Alfanumérica</td>";
                } else if (row.tipo == 4) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">Abierta Numérica</td>";
                }

                if (row.obligatorio == 1) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">Si</td>";
                } else {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\">No</td>";
                }

                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\" class='cantidad-respuestas'>" + row.cant_resp + "</td>";

                if (row.estado == 1) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\"><i class=\"glyphicon glyphicon-ok-circle estado_pre\" style=\"cursor: pointer; font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_pre=\"" + row.id_pregunta + "\" data-estado=\"1\" data-pregunta_enc=\"" + row.pregunta_enc + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\"></i></td>";
                } else if (row.estado == 0) {
                    html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\"><i class=\"glyphicon glyphicon-ban-circle estado_pre\" style=\"cursor: pointer; font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_pre=\"" + row.id_pregunta + "\" data-estado=\"0\" data-pregunta_enc=\"" + row.pregunta_enc + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\"></i></td>";
                }

                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\"><i class=\"glyphicon glyphicon-remove-sign eliminarPregunta\" title=\"Eliminar\" style=\"cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_pre=\"" + row.id_pregunta + "\" data-pregunta_enc=\"" + row.pregunta_enc + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\"></i></td>";
                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\"><i class=\"glyphicon glyphicon-duplicate duplicarPregunta\" style=\"font-size: 25px; color: #10628a; margin-top: 8px;cursor:pointer;\" data-id_pre=\"" + row.id_pregunta + "\" data-obliga=\"" + row.obligatorio + "\" data-tipo=\"" + row.tipo + "\" data-orden=\"" + row.orden + "\" data-pregunta_enc=\"" + row.pregunta_enc + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\"></i></td>";
                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;\"><button class=\"btn btn-sm btn-primary editar_pre\" data-id_pre=\"" + row.id_pregunta + "\" data-obliga=\"" + row.obligatorio + "\" data-tipo=\"" + row.tipo + "\" data-orden=\"" + row.orden + "\" data-pregunta_enc=\"" + row.pregunta_enc + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + id_respuesta + "\"><i class=\"fa fa-edit\" title=\"Editar\"></i></button>";

                let class_pre = "";
                let color_btn = "#c5c5c5";

                if (row.tipo < 3) {
                    class_pre = "s_respuestas";
                    color_btn = "#10628a";
                }

                html += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;border-right: 1px solid #006591;\"><i class=\"glyphicon glyphicon-plus-sign " + class_pre + "\" title=\"Agregar Respuesta\" style=\"font-size: 25px; color: " + color_btn + "; margin-top: 8px;cursor:pointer;\" data-id_pre=\"" + row.id_pregunta + "\" data-vigencia=\"" + vigencia + "\" data-id_encuesta=\"" + id_encuesta + "\"></i></td>";
                html += "</tr>";
                html += "<tr id = \"pregunta_" + row.id_pregunta + "\" style=\"display: none\"><td colspan=\"11\" id = \"respuestas_" + row.id_pregunta + "\" style=\"padding-left: " + padding + ";\"></td></tr>";
            });

            html += "</tbody>";
            html += "</table>";

            if (id_respuesta == 0) {
                if (row)
                    row.child(html).show();

                if (tr)
                    tr.addClass("shown");
            } else {
                $("#preguntas_" + id_encuesta + "_" + id_respuesta).html(html);
                $("#pregunta_" + id_encuesta + "_" + id_respuesta).show();
            }

            $(".btnDesplegarRespuestas").unbind("click");
            $(".btnDesplegarRespuestas").click(function () {
                id_enc = $(this).attr("data-id_encuesta");
                let id_pregunta = $(this).attr("data-id_pre");
                let tipo = $(this).attr("data-tipo");

                crear_vista_respuestas(id_pregunta, id_enc, this, 0, tipo);
            });

            $(".duplicarPregunta").unbind("click");
            $(".duplicarPregunta").click(function () {
                limpiar_form(2);
                ver_orden(parseInt($(this).data("orden")) - 1, 1, 1);

                let id_encuesta = $(this).data("id_encuesta");
                let id_pregunta = $(this).data("id_pre");
                let preguntaTxt = $(this).data("pregunta_enc");
                let obliga = $(this).data("obliga");
                let tipo = $(this).data("tipo");
                let orden = $(this).data("orden");

                id_resp = $(this).data("id_respuesta");
                tr_pre = tr;
                row_pre = row;
                id_btn_pre = btn;

                modal_preguntas(id_encuesta, 0, id_pregunta, id_resp, preguntaTxt, obliga, tipo, orden, 2, 1);
            });

            $(".editar_pre").unbind("click");
            $(".editar_pre").click(function () {
                limpiar_form(2);
                ver_orden(parseInt($(this).data("orden")) - 1, 1, 1);

                let id_encuesta = $(this).data("id_encuesta");
                let id_pregunta = $(this).data("id_pre");
                let preguntaTxt = $(this).data("pregunta_enc");
                let obliga = $(this).data("obliga");
                let tipo = $(this).data("tipo");
                let vigencia = $(this).data("vigencia");
                let orden = $(this).data("orden");

                id_resp = $(this).data("id_respuesta");
                tr_pre = tr;
                row_pre = row;
                id_btn_pre = btn;

                modal_preguntas(id_encuesta, vigencia, id_pregunta, id_resp, preguntaTxt, obliga, tipo, orden, 1, 1);
            });

            $(".estado_pre").unbind("click");
            $(".estado_pre").click(function () {
                if ($(this).data("vigencia") == 0) {
                    let estadoThis = this;
                    let estado = $(this).data("estado");

                    id_enc = $(this).data("id_encuesta");
                    id_pre = $(this).data("id_pre");
                    id_resp = $(this).data("id_respuesta");

                    BootstrapDialog.confirm("¿Está seguro de cambiar el estado de la pregunta?", function (result) {
                        if (result) {
                            $.post("modulos/crear_preguntas_v1.0.0/controlador.php", {
                                accion: "cambiar_estado",
                                id_enc: id_enc,
                                estado: estado,
                                id_pre: id_pre
                            }, (data) => {
                                data = JSON.parse(data);

                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");

                                    if (estado == 0) {
                                        $(estadoThis).removeClass("glyphicon-ban-circle");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).removeClass("glyphicon-ban-circle");

                                        $(estadoThis).addClass("glyphicon-ok-circle");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).addClass("glyphicon-ok-circle");

                                        $(estadoThis).css("color", "#10628a");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).css("color", "#10628a");

                                        $(estadoThis).data("estado", 1);
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).data("estado", 1);
                                    } else {
                                        $(estadoThis).removeClass("glyphicon-ok-circle");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).removeClass("glyphicon-ok-circle");

                                        $(estadoThis).addClass("glyphicon-ban-circle");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).addClass("glyphicon-ban-circle");

                                        $(estadoThis).css("color", "#de0a0b");
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).css("color", "#de0a0b");

                                        $(estadoThis).data("estado", 0);
                                        $(`#respuestas_${id_pre} table tbody tr td .estado_resp`).data("estado", 0);
                                    }
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                            });
                        }
                    });
                } else {
                    BootstrapDialog.show({
                        title: $(this).data("pregunta_enc"),
                        closable: true,
                        closeByBackdrop: false,
                        closeByKeyboard: false,
                        size: BootstrapDialog.SIZE_WIDE,
                        message: "<div style=\"text-align: center;\">No es posible cambiar el estado de la pregunta mientras la encuesta esté vigente</div>",
                        buttons: [{
                            label: "Cerrar",
                            cssClass: "btn",
                            action: function (dialog) {
                                dialog.close();
                            }
                        }]
                    });
                }
            });

            $(".eliminarPregunta").unbind("click");
            $(".eliminarPregunta").click(function () {
                let element = this;

                id_enc = $(element).data("id_encuesta");
                id_pre = $(element).data("id_pre");
                id_resp = $(element).data("id_respuesta");

                BootstrapDialog.confirm("¿Está seguro de <b style='color:red'>ELIMINAR</b> la pregunta?", function (result) {
                    if (result) {
                        $.post("modulos/crear_preguntas_v1.0.0/controlador.php", {
                            accion: "eliminar",
                            id_enc: id_enc,
                            id_pre: id_pre
                        },

                            function (data) {
                                data = JSON.parse(data);
                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");
                                    $("#mover_" + id_pre + "_" + id_resp).slideUp(1000);
                                    $("#pregunta_" + id_pre).hide();

                                    let cantidad_preg = $(element).parents(".moverRespuestas").children("tr:first-child").children("td.cantidas-preguntas").html();

                                    $(element).parents(".moverRespuestas").children("tr:first-child").children("td.cantidas-preguntas").html((cantidad_preg - 1))
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                            }
                        );
                    }
                });
            });

            $(".s_respuestas").unbind("click");
            $(".s_respuestas").click(function () {
                let id_pregunta = $(this).attr("data-id_pre");

                $("#ver_opciones_duplicar_respuesta").hide();
                $("#btn_resp").html("Crear");

                if ($(this).data("vigencia") == 0) {
                    $("#titulo_respuesta").html("Crear Respuestas");
                    limpiar_form(3);

                    id_enc = $(this).data("id_encuesta");
                    id_pre = id_pregunta;
                    id_resp = 0;
                    cant_resp = 0;
                    tipo_crud_resp = 1;

                    ver_orden(0, 0, 2);
                    crear_vista_rama(id_encuesta, id_pre, id_resp, 0, 1);

                    $("#modal_crud_respuestas").modal("show");
                } else {
                    BootstrapDialog.show({
                        title: "Crear Preguntas",
                        closable: true,
                        closeByBackdrop: false,
                        closeByKeyboard: false,
                        size: BootstrapDialog.SIZE_WIDE,
                        message: "<div style=\"text-align: center;\">No es posible crear la respuesta mientras la encuesta esté vigente</div>",
                        buttons: [{
                            label: "Cerrar",
                            cssClass: "btn",
                            action: function (dialog) {
                                dialog.close();
                            }
                        }]
                    });
                }
            });

            $("table tbody .moverPreguntas").sortable({
                placeholder: "ui-state-highlight",

                activate: function (event, ui) {
                    let id_pre = ui.item.data("id_pre");
                    let id_respuesta = ui.item.data("id_respuesta");
                    let button = $(ui.item.context).find("td > button.btnDesplegarRespuestas");

                    if (button.children("span").hasClass("noDesplegar"))
                        button.trigger("click");

                    if (!id_pre || !id_respuesta)
                        return;

                    $("#mover_" + id_pre + "_" + id_respuesta).children(".btnDesplegarRespuestas").trigger("click");
                    $("#mover_" + id_pre + "_" + id_respuesta).css("background-color", "#d8d62b");
                },
                update: function (event, ui) {
                    let id_pre = ui.item.data("id_pre");
                    let id_enc = ui.item.data("id_encuesta");
                    let id_respuesta = ui.item.data("id_respuesta");
                    let i = 0;

                    if (!id_pre)
                        return;

                    $("#encuesta_" + id_enc + "_" + id_respuesta + " > tbody > tr").each(function (index) {
                        if ($(this).attr("id") === `mover_${id_pre}_${id_respuesta}`) {
                            if (index == 0) {
                                i = 1;
                            } else {
                                i = index;
                            }
                        }
                    });

                    ordenar(1, id_encuesta, id_respuesta, id_pre, vigencia, i, tr, row, 0);
                }
            });

            $("table tbody .moverPreguntas").disableSelection();
        });
    } else {
        $(btn).children().removeClass("glyphicon glyphicon-minus noDesplegar");
        $(btn).children().addClass("glyphicon glyphicon-plus desplegar");

        if (id_respuesta == 0) {
            row.child.hide();
            tr.removeClass("shown");
        } else {
            $("#pregunta_" + id_encuesta + "_" + id_respuesta).hide();
        }
    }
}

function crear_vista_respuestas(id_pregunta, id_encuesta, btn, op, tipo_pre) {
    if ($(btn).children().hasClass("desplegar") || (!$(btn).children().hasClass("desplegar") && op == 1)) {
        if (op !== 1) {
            $(btn).children().removeClass("glyphicon glyphicon-plus desplegar").change();
            $(btn).children().addClass("glyphicon glyphicon-minus noDesplegar").change();
        }

        fetch("modulos/crear_respuestas_v1.0.0/controlador.php", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: "ver_respuestas",
                id_prel: id_pregunta
            })
        }).then(response => response.json()).then(dataR => {
            let htmlR;

            htmlR = "<table style=\"width: calc(100% - 14px); border-left: 4px solid #00638d; margin: 14px;\" id = \"pregunta_" + id_pregunta + "\">";
            htmlR += "<thead>";
            htmlR += "<tr>";

            htmlR += "<th style=\"width: 5%;\"></th>";

            if (tipo_pre != 2)
                htmlR += "<th style=\"width: 5%;\"></th>";

            htmlR += "<th style=\"width: 25%;text-align: center;\">RESPUESTAS</th>";
            htmlR += "<th style=\"width: 25%;text-align: center;\">CANTIDAD PREGUNTAS</th>";
            htmlR += "<th style=\"width: 25%;text-align: center;\">ESTADO</th>";
            htmlR += "<th style=\"width: 25%;text-align: center;\">ELIMINAR</th>";
            htmlR += "<th style=\"width: 25%;text-align: center;\">DUPLICAR</th>";
            htmlR += "<th style=\"width: 25%;text-align: center;\">EDITAR</th>";

            if (tipo_pre != 2)
                htmlR += "<th style=\"width: 25%;text-align: center;\">AGREGAR PREGUNTA</th>";

            htmlR += "</tr>";
            htmlR += "</thead>";
            htmlR += "<tbody class=\"moverRespuestas\">";

            $.each(dataR, (index, rowR) => {
                htmlR += "<tr style=\"cursor:move;\" class=\"moveActive2\" id=\"moverR_" + rowR.id + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_respuesta=\"" + rowR.id + "\">";

                let color = "#00638d";
                let classBtn = "btnDesplegarSubPreguntas";

                htmlR += "<td style=\"text-align: right;\"><div style=\"background-color: #00638d;width: 12px;height: 12px;border-radius: 50%;position: relative;right: 8px;\"></div></td>";

                if (tipo_pre != 2) {
                    htmlR += "<td style=\"background-color: #fff; text-align: left;padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;border-left: 1px solid #006591;\"><button type=\"button\" class=\"btn " + classBtn + "\" data-id_resp=\"" + rowR.id + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\" style=\"border: 1px solid " + color + ";color: " + color + ";\"><span class=\"glyphicon glyphicon-plus desplegar\" aria-hidden=\"true\" style=\"font-size: 12px;\"></span></button></td>";
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\">" + rowR.respuesta + "</td>";
                } else {
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;border-left: 1px solid #006591;\">" + rowR.respuesta + "</td>";
                }

                htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\" class=\"cantidas-preguntas\">" + rowR.cant_pre + "</td>";

                if (rowR.estado == 1) {
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\"><i class=\"glyphicon glyphicon-ok-circle estado_resp\" title=\"Activo\" style=\"cursor:pointer; font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_resp=\"" + rowR.id + "\" data-estado=\"1\" data-id_encuesta=\"" + id_encuesta + "\" data-id_pregunta=\"" + id_pregunta + "\"></i></td>";
                } else {
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\"><i class=\"glyphicon glyphicon-ban-circle estado_resp\" title=\"Inactivo\" style=\"cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_resp=\"" + rowR.id + "\" data-estado=\"0\" data-id_encuesta=\"" + id_encuesta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_pregunta=\"" + id_pregunta + "\"></i></td>";
                }

                htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\"><i class=\"glyphicon glyphicon-remove-sign eliminarRespuesta\" title=\"Eliminar\" style=\"cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 8px;\" data-id_resp=\"" + rowR.id + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-id_pregunta=\"" + id_pregunta + "\"></i></td>";
                htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\"><i class=\"glyphicon glyphicon-duplicate duplicarRespuesta\" style=\"cursor:pointer;font-size: 25px; color: #10628a; margin-top: 8px;\" data-id_resp=\"" + rowR.id + "\" data-orden=\"" + rowR.orden + "\" data-respuesta = \"" + rowR.respuesta + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\" data-cantidad_pre='" + rowR.cant_pre + "'></i></td>";

                if (tipo_pre != 2) {
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;text-align: center;\"><button class=\"btn btn-sm btn-primary editar_resp\" data-id_resp=\"" + rowR.id + "\" data-orden=\"" + rowR.orden + "\" data-respuesta = \"" + rowR.respuesta + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\"><i class=\"fa fa-edit\" title=\"Editar\"></i></button></td>";
                } else {
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;border-right: 1px solid #006591;text-align: center;\"><button class=\"btn btn-sm btn-primary editar_resp\" data-id_resp=\"" + rowR.id + "\" data-orden=\"" + rowR.orden + "\" data-respuesta = \"" + rowR.respuesta + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\"><i class=\"fa fa-edit\" title=\"Editar\"></i></button></td>";
                }

                let class_pre = "";
                let color_btn = "#c5c5c5";

                if (tipo_pre == 1) {
                    class_pre = "resp_presguntas";
                    color_btn = "#10628a";
                }

                if (tipo_pre != 2)
                    htmlR += "<td style=\"background-color: #fff; padding: 10px;border-top: 1px solid #006591;border-bottom: 1px solid #006591;border-right: 1px solid #006591;text-align: center;\"><i class=\"glyphicon glyphicon-plus-sign " + class_pre + "\" title=\"Agregar Pregunta\" style=\"font-size: 25px; color: " + color_btn + "; margin-top: 8px;cursor:pointer;\" data-id_resp=\"" + rowR.id + "\" data-id_pregunta=\"" + id_pregunta + "\" data-id_encuesta=\"" + id_encuesta + "\"></i></td>";

                htmlR += "</tr>";
                htmlR += "<tr id = \"pregunta_" + id_encuesta + "_" + rowR.id + "\" style=\"display:none\"><td colspan=\"10\" id = \"preguntas_" + id_encuesta + "_" + rowR.id + "\" style=\"padding-left: 55px;padding-top: 15px;padding-bottom: 15px;\"></td></tr>";
            });

            htmlR += "</tbody>";
            htmlR += "</table>";

            $("#respuestas_" + id_pregunta).html(htmlR);
            $("#pregunta_" + id_pregunta).slideDown();

            $(".duplicarRespuesta").unbind("click");
            $(".duplicarRespuesta").click(function () {
                limpiar_form(3);
                $("#seleccionar_pregunta_respuesta").html("").change();

                id_encuesta_origen = $(this).data("id_encuesta");
                id_pre_origen = $(this).data("id_pregunta");
                id_resp = $(this).data("id_resp");

                $("#respuesta").val($(this).data("respuesta"));
                $("#titulo_respuesta").html("Duplicar Respuesta");
                $("#btn_resp").html("Duplicar");

                tipo_crud_resp = 3;
                tipo_pregunta = tipo_pre;

                crear_vista_rama(id_encuesta, id_pre, id_resp, 0, 0);

                $("#ver_opciones_duplicar_respuesta").show();
                cargar_titulos(2);

                let cantidad_pre = $(this).data("cantidad_pre");

                //$("#seleccionar_encuesta_respuesta").unbind("change");
                $("#seleccionar_encuesta_respuesta").change(function () {
                    if ($(this).val() !== "") {
                        let idEncuesta = $(this).val();

                        cargar_preguntas(idEncuesta, 0, 1, cantidad_pre);
                    }

                    $("#seleccionar_pregunta_respuesta").val("");
                });

                $("#seleccionar_pregunta_respuesta").change(function () {
                    if ($(this).val() !== "") {
                        let idPregunta = $(this).val();

                        id_enc = $("#seleccionar_encuesta_respuesta option:selected").val();
                        id_pre = idPregunta;

                        ver_orden(0, 0, 2);
                    }
                });

                $("#modal_crud_respuestas").modal("show");
            });

            $(".editar_resp").click(function () {
                limpiar_form(3);
                $("#ver_opciones_duplicar_respuesta").hide();
                $("#btn_resp").html("Editar");

                id_enc = $(this).data("id_encuesta");
                id_pre = $(this).data("id_pregunta");

                ver_orden(parseInt($(this).attr("data-orden")) - 1, 1, 2);

                id_resp = $(this).data("id_resp");
                $("#respuesta").val($(this).data("respuesta"));
                $("#titulo_respuesta").html("Editar Respuesta");

                tipo_crud_resp = 2;
                tipo_pregunta = tipo_pre;

                crear_vista_rama(id_encuesta, id_pre, id_resp, 0, 0);

                $("#modal_crud_respuestas").modal("show");

            });

            $(".estado_resp").unbind("click");
            $(".estado_resp").click(function () {
                let estadoThis = this;
                let estado = $(this).attr("data-estado");

                id_enc = $(this).data("id_encuesta");
                id_resp = $(this).data("id_resp");
                id_pre = $(this).data("id_pregunta");

                BootstrapDialog.confirm("¿Está seguro de cambiar el estado de la respuesta?", function (result) {
                    if (result) {
                        fetch("modulos/crear_respuestas_v1.0.0/controlador.php", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                accion: "cambiar_estado",
                                estado: estado,
                                id_resp: id_resp,
                                id_enc: id_enc
                            })
                        }).then(response => response.json()).then(data => {

                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");

                                    if (estado == 0) {
                                        $(estadoThis).removeClass("glyphicon glyphicon-ban-circle");
                                        $(estadoThis).addClass("glyphicon glyphicon-ok-circle");
                                        $(estadoThis).css("color", "#10628a");
                                        $(estadoThis).attr("data-estado", 1);
                                    } else {
                                        $(estadoThis).removeClass("glyphicon glyphicon-ok-circle");
                                        $(estadoThis).addClass("glyphicon glyphicon-ban-circle");
                                        $(estadoThis).css("color", "#de0a0b");
                                        $(estadoThis).attr("data-estado", 0);
                                    }

                                    let items_state = $(estadoThis).parent().parent().parent().children(".moveActive2").children("td").children(".estado_resp");
                                    let item_state_parent = $(`tr[data-id_pre=${id_pre}]`).children("td").children(".estado_pre");

                                    if (!items_state.hasClass("glyphicon-ok-circle")) {
                                        item_state_parent.removeClass("glyphicon glyphicon-ok-circle");
                                        item_state_parent.addClass("glyphicon glyphicon-ban-circle");
                                        item_state_parent.css("color", "#de0a0b");
                                        item_state_parent.attr("data-estado", 0);
                                    }
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                        }).catch(error => {
                            console.error('Error:', error);
                        });
                    }
                });
            });

            $(".eliminarRespuesta").unbind("click");
            $(".eliminarRespuesta").click(function () {
                let element = this;
                id_enc = $(element).data("id_encuesta");
                id_resp = $(element).data("id_resp");
                id_pre = $(element).data("id_pregunta");

                BootstrapDialog.confirm("¿Está seguro de <b style='color:red'>ELIMINAR</b> la respuesta?", function (result) {
                    if (result) {
                        fetch("modulos/crear_respuestas_v1.0.0/controlador.php", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                accion: "eliminar",
                                id_resp: id_resp,
                                id_enc: id_enc
                            })
                        }).then(response => response.json()).then(data => {

                                if (data["estado"] == 1) {
                                    Notificacion(data["msg"], "success");

                                    $("#moverR_" + id_resp).slideUp(1000);
                                    $("#pregunta_" + id_enc + "_" + id_resp).slideUp("");

                                    let cantidad_preg = $(element).parents(".moverPreguntas").children(".moverPre").children(".cantidad-respuestas").html();

                                    $(element).parents(".moverPreguntas").children(".moverPre").children(".cantidad-respuestas").html((cantidad_preg - 1))
                                } else if (data["estado"] == 0) {
                                    Notificacion(data["msg"], "error");
                                }
                        }).catch(error => {
                            console.error('Error:', error);
                        });

                    }
                });
            });

            $(".btnDesplegarSubPreguntas").unbind("click");
            $(".btnDesplegarSubPreguntas").click(function () {
                let id_encuesta = $(this).data("id_encuesta");
                let id_respuesta = $(this).data("id_resp");

                crear_vista_preguntas(id_encuesta, id_respuesta, 0, 0, 0, this, val_vigencia, 0, 0);
            });

            $(".resp_presguntas").unbind("click");
            $(".resp_presguntas").click(function () {
                let id_encuesta = $(this).data("id_encuesta");
                let id_pregunta = $(this).data("id_pregunta");
                let id_respuesta = $(this).data("id_resp");
                let vigencia = val_vigencia;

                tipo_pregunta = tipo_pre;

                modal_preguntas(id_encuesta, vigencia, id_pregunta, id_respuesta, "", 0, 0, 0, 0, 0);
            });

            $("table tbody .moverRespuestas").sortable({
                placeholder: "ui-state-highlight",

                activate: function (event, ui) {
                    let id_pre = ui.item.data("id_pre");
                    let id_respuesta = ui.item.data("id_respuesta");

                    let button = $(ui.item.context).find("td > button.btnDesplegarRespuestas");

                    if (button.children("span").hasClass("noDesplegar"))
                        button.trigger("click");

                    if (!id_pre) return;

                    $("#moverR_" + id_respuesta).children(".btnDesplegarSubPreguntas").trigger("click");
                    $("#moverR_" + id_respuesta).css("background-color", "#d8d62b");

                    $("#moverR_" + id_respuesta).delay(2000).queue(function (next) {
                        $("#moverR_" + id_respuesta).css("background-color", "inherit");
                        next();
                    });
                },

                update: function (event, ui) {
                    let id_pre = ui.item.data("id_pregunta");
                    let id_enc = ui.item.data("id_encuesta");
                    let id_respuesta = ui.item.data("id_respuesta");
                    let i = 0;

                    if (!id_pre) return;

                    $("#moverR_" + id_respuesta).css("background-color", "#d8d62b");

                    $("#pregunta_" + id_pre + " > tbody > tr").each(function (index) {
                        if ($(this).attr("id") == "moverR_" + id_respuesta) {
                            if (index == 0) {
                                i = 1;
                            } else {
                                i = index;
                            }
                        }
                    });

                    ordenar(2, id_enc, id_respuesta, id_pre, val_vigencia, i, 0, 0, tipo_pre);
                }
            });

        }).catch(error => {
            console.error('Error:', error);
        });
    } else {
        $(btn).children().removeClass("glyphicon glyphicon-minus noDesplegar");
        $(btn).children().addClass("glyphicon glyphicon-plus desplegar");
        $("#pregunta_" + id_pregunta).slideUp();
    }

}

function ordenar(op, id_encuesta, id_respuesta, id_pregunta, vigencia, posicion, tr, row, tipo_pregunta) {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "ordenar",
            op: op,
            id_encuesta: id_encuesta,
            id_respuesta: id_respuesta,
            id_pregunta: id_pregunta,
            posicion: posicion
        })
    }).then(response => response.json()).then(data => {

        if (op == 1) {
            let llamarRespuestas = 0;
            let id_btn = "#btn_" + id_pregunta;

            if (!$(id_btn).children().hasClass("desplegar")) {
                llamarRespuestas = 1;
            }

            if (data.estado == 0) {
                Notificacion(data.msg, "error");
            }

            $("#mover_" + id_pregunta).delay(2000).queue(function (next) {
                $("#mover_" + id_pregunta).css("background-color", "inherit");
                next();
            });

            crear_vista_preguntas(id_encuesta, id_respuesta, id_pregunta, tr, row, id_btn, vigencia, 1, llamarRespuestas);

        } else {

            let id_btn = "#btn_" + id_pregunta;

            if (data.estado == 0) {
                Notificacion(data.msg, "error");
            }

            crear_vista_respuestas(id_pregunta, id_encuesta, id_btn, 1, tipo_pregunta);

            $("#moverR_" + id_respuesta).delay(2000).queue(function (next) {
                $("#moverR_" + id_respuesta).css("background-color", "inherit");
                next();
            });
        }
    }).catch(error => {
        console.error('Error:', error);
    });
}

function crud_encuesta() {
    let titulo = $("#titulo").val();
    let descrip_e = $("#descrip_e").val();
    let fecha_ini = $("#fecha_ini").val();
    let fecha_fin = $("#fecha_fin").val();
    let check_obliga = $("input:radio[name=c_obliga]:checked").val();
    let check_navega = $("input:radio[name=c_navega]:checked").val();
    let aplicar_encuesta = $("input:radio[name=aplicar_encuesta]:checked").val();
    let reporte_distri = $("input:radio[name=reporte_distri]:checked").val();

    let msg = "";
    let dia_act = d.getDate();
    let mes_act = d.getMonth() + 1;
    let anio = d.getFullYear();
    let fecha_act = anio + "-" + mes_act + "-" + dia_act;

    if (val_vigencia == 0 && perm == 1 && fecha_ini <= fecha_act && fecha_fin >= fecha_act) {
        msg = "<br><br><b>NOTA:</b> La encuesta estará en vigencia a partir de que se asignen los permisos. Se debe tener en cuenta que no se puede editar una encuesta mientras esté vigente";
    }

    BootstrapDialog.confirm("¿Está seguro de enviar los datos? " + msg, function (result) {
        if (result) {

            fetch(`${URL_MODULO}/controlador.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    accion: "crud_encuesta",
                    titulo: titulo,
                    descrip_e: descrip_e,
                    fecha_ini: fecha_ini,
                    fecha_fin: fecha_fin,
                    check_obliga: check_obliga,
                    check_navega: check_navega,
                    tipo_crud: tipo_crud,
                    id_encuesta: id_enc,
                    aplicar_encuesta: aplicar_encuesta,
                    reporte_distri: reporte_distri
                })
            }).then(response => response.json()).then(data => {

                $.each(data, function (index, fila) {
                    if (fila.estado == 1) {
                        Notificacion(fila.msg, "success");
                        Cargar_Tabla();
                        limpiar_form(1);

                        $(".modal").modal("hide");
                    } else {
                        Notificacion(fila.msg, "error");
                    }
                });
            }).catch(error => {
                console.error('Error:', error);
            });
        }
    });
}

function crud_preguntas() {
    let pregunta = $("#pregunta").val();
    let orden = $("#orden_pregunta").val();
    let tipopc = $("#tipopc").val();
    let check_obliga = $("input:radio[name=c_obligaPre]:checked").val();

    if (tipo_crud_pre == 3) {
        if ($("#seleccionar_encuesta option:selected").val() !== "") {
            id_enc = $("#seleccionar_encuesta option:selected").val();
        } else {
            Notificacion("Es necesario seleccionar una encuesta", "warning");
            return false;
        }

        if ($("#seleccionar_pregunta option:selected").val() !== "" && $("#seleccionar_pregunta option:selected").val() !== null && $("#seleccionar_pregunta option:selected").val() !== undefined) {
            id_pre = $("#seleccionar_pregunta option:selected").val();

            if ($("#seleccionar_respuesta option:selected").val() !== "") {
                id_resp = $("#seleccionar_respuesta option:selected").val();
            } else {
                Notificacion("Es necesario seleccionar una respuesta", "warning");
                return false;
            }
        } else {
            if ($("#seleccionar_respuesta option:selected").val() !== "" && $("#seleccionar_respuesta option:selected").val() !== null && $("#seleccionar_respuesta option:selected").val() !== undefined) {
                Notificacion("Es necesario seleccionar una pregunta", "warning");
                return false;
            }
        }
    }

    BootstrapDialog.confirm("¿Está seguro de enviar los datos?", function (result) {
        if (result) {

            fetch("modulos/crear_preguntas_v1.0.0/controlador.php", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    accion: "crud",
                    pregunta: pregunta,
                    orden: orden,
                    tipopc: tipopc,
                    check_obliga: check_obliga,
                    tipo_crud: tipo_crud_pre,
                    id_pre: id_pre,
                    id_enc: id_enc,
                    id_respuesta: id_resp,
                    id_encuesta_origen: id_encuesta_origen,
                    id_pre_origen: id_pre_origen,
                    id_resp_origen: id_resp_origen
                })
            }).then(response => response.json()).then(data => {

                    $.each(data, function (index, fila) {
                        if (fila.estado == 1) {
                            Notificacion(fila.msg, "success");

                            if (tipo_crud_pre == 1) {
                                if (id_pre > 0 && id_resp > 0) {
                                    if ($("button:button[type='button'][data-id_encuesta='" + id_enc + "'][data-id_pregunta='" + id_pre + "'][data-id_resp='" + id_resp + "']").children().hasClass("desplegar")) {
                                        $("button:button[type='button'][data-id_encuesta='" + id_enc + "'][data-id_pregunta='" + id_pre + "'][data-id_resp='" + id_resp + "']").click();
                                    } else {
                                        $("button:button[type='button'][data-id_encuesta='" + id_enc + "'][data-id_pregunta='" + id_pre + "'][data-id_resp='" + id_resp + "']").click();
                                        $("button:button[type='button'][data-id_encuesta='" + id_enc + "'][data-id_pregunta='" + id_pre + "'][data-id_resp='" + id_resp + "']").click();
                                    }
                                } else {
                                    if ($("button:button[name='btnDesplegarPreguntas'][data-id_enc='" + id_enc + "']").children().hasClass("desplegar")) {
                                        $("button:button[name='btnDesplegarPreguntas'][data-id_enc='" + id_enc + "']").click();
                                    } else {
                                        $("button:button[name='btnDesplegarPreguntas'][data-id_enc='" + id_enc + "']").click();
                                        $("button:button[name='btnDesplegarPreguntas'][data-id_enc='" + id_enc + "']").click();
                                    }
                                }
                            } else {
                                crear_vista_preguntas(id_enc, id_resp, 0, tr_pre, row_pre, id_btn_pre, val_vigencia, 1, 0);
                            }

                            $(".modal").modal("hide");
                        } else {
                            Notificacion(fila.msg, "error");
                        }
                    });
            }).catch(error => {
                console.error('Error:', error);
            });
        }
    });
}

function crud_respuestas() {
    let respuesta = $("#respuesta").val();
    let orden = $("#orden_respuesta").val();

    if (respuesta === "" || orden === "") {
        Notificacion("Todos los campos son obligatorios");
        return false;
    }

    if (tipo_crud_resp == 3) {
        if ($("#seleccionar_encuesta_respuesta option:selected").val() === "") {
            Notificacion("Es necesario seleccionar una encuesta");
            return false;
        }

        if ($("#seleccionar_pregunta_respuesta option:selected").val() === "") {
            Notificacion("Es necesario seleccionar una pregunta");
            return false;
        }
    }

    BootstrapDialog.confirm("¿Está seguro de enviar los datos?", function (result) {
        if (result) {
            fetch("modulos/crear_respuestas_v1.0.0/controlador.php", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    accion: "crud",
                    respuesta: respuesta,
                    orden: orden,
                    tipo_crud: tipo_crud_resp,
                    id_enc: id_enc,
                    id_prel: id_pre,
                    id_resp: id_resp,
                    id_encuesta_origen: id_encuesta_origen,
                    id_pre_origen: id_pre_origen
                })
            }).then(response => response.json()).then(data => {

                    $.each(data, function (index, fila) {
                        if (fila.estado == 1) {
                            Notificacion(fila.msg, "success");

                            if (tipo_crud_resp == 1) {
                                if ($("#btn_" + id_pre).children().hasClass("desplegar")) {
                                    $("#btn_" + id_pre).click();
                                } else {
                                    $("#btn_" + id_pre).click();
                                    $("#btn_" + id_pre).click();
                                }
                            } else {
                                let id_btn = "#btn_" + id_pre;
                                crear_vista_respuestas(id_pre, id_enc, id_btn, 1, tipo_pregunta);
                            }

                            $(".modal").modal("hide");
                        } else {
                            Notificacion(fila.msg, "error");
                        }
                    });
            }).catch(error => {
                console.error('Error:', error);
            });
        }
    });
}

function modal_preguntas(id_encuesta, vigencia, id_pregunta, id_respuesta, preguntaTxt, obliga, tipo, orden, crud, op) {

    if (vigencia == 0) {

        crear_vista_rama(id_encuesta, id_pregunta, id_respuesta, tipo, op);

        limpiar_form(2);

        if (crud == 0) {

            let select = "";

            $("#titulo_pregunta").html("Crear Pregunta");
            $("#btn_pregunta").html("Crear");

            $("#tipopc").html("");
            select += "<option value = \"\" >Seleccionar...</option>";
            select += "<option value = \"1\">Única</option>";
            select += "<option value = \"2\">Múltiple</option>";
            select += "<option value = \"3\">Abierta Alfanumérica</option>";
            select += "<option value = \"4\">Abierta Numérica</option>";

            $("#tipopc").html(select).change();
            $("#tipopc").val("").prop("selected", true).change();

            id_enc = id_encuesta;
            id_pre = id_pregunta;
            id_resp = id_respuesta;
            cant_resp = 0;
            ver_orden(0, 0, 1);
            tipo_crud_pre = 1;
            $("#ver_opciones_duplicar").hide();
            $("#modal_crud_preguntas").modal("show"); // Abre el modal donde se crea el formulario.

        } else {

            id_enc = id_encuesta;
            id_pre = id_pregunta;
            val_vigencia = vigencia;
            id_resp = id_respuesta;
            $("#pregunta").val(preguntaTxt);

            $("input:radio[name=c_obligaPre][value='" + obliga + "']").prop("checked", true);

            $("#tipopc").val(tipo).prop("selected", true).change();

            $("#modal_crud_preguntas").modal("show");
            if (crud == 1) {
                $("#titulo_pregunta").html("Editar Pregunta");
                $("#btn_pregunta").html("Editar");
                $("#ver_opciones_duplicar").hide();
                tipo_crud_pre = 2;

                ver_orden(parseInt(orden) - 1, 1, 1);
            }

            if (crud == 2) {

                id_encuesta_origen = id_encuesta;
                id_pre_origen = id_pregunta;
                id_resp_origen = id_respuesta;

                $("#titulo_pregunta").html("Duplicar Pregunta");
                $("#btn_pregunta").html("Duplicar");
                $("#ver_opciones_duplicar").show();
                tipo_crud_pre = 3;
                cargar_titulos(1);

                //$("#seleccionar_encuesta").unbind("change");
                $("#seleccionar_encuesta").change(function () {
                    if ($(this).val() != "") {
                        let idEncuesta = $(this).val();
                        //let permiso = $("#idRegio option:selected").attr("data-permiso");
                        cargar_preguntas(idEncuesta, 0, 0);

                        id_enc = idEncuesta;
                        id_pre = 0;
                        id_resp = 0;

                        ver_orden(0, 0, 1);

                    }
                    $("#seleccionar_pregunta").val("");
                    $("#seleccionar_respuesta").html("").change();
                });

                $("#seleccionar_pregunta").change(function () {

                    if ($(this).val() != "") {

                        let idPregunta = $(this).val();
                        let idEncuesta = $("#seleccionar_encuesta option:selected").val();
                        cargar_respuestas(idEncuesta, idPregunta);
                    }
                });

                $("#seleccionar_respuesta").change(function () {
                    if ($(this).val() != "" && $(this).val() !== null) {

                        let idRespuesta = $(this).val();
                        let idPregunta = $("#seleccionar_pregunta option:selected").val();

                        id_enc = $("#seleccionar_encuesta option:selected").val();
                        id_pre = idPregunta;
                        id_resp = idRespuesta;

                        ver_orden(0, 0, 1);
                    }
                });

                if (tipo != 1) {
                    $("#seleccionar_pregunta").val("");
                    $("#seleccionar_respuesta").val("");
                    $("#seleccionar_pregunta").prop("disabled", true);
                    $("#seleccionar_respuesta").prop("disabled", true);
                }

                $("#tipopc").change(function () {
                    let tipoPregunta = $(this).val();
                    if (tipoPregunta != 1) {
                        $("#seleccionar_pregunta").val("");
                        $("#seleccionar_respuesta").val("");
                        $("#seleccionar_pregunta").prop("disabled", true);
                        $("#seleccionar_respuesta").prop("disabled", true);
                    } else {
                        $("#seleccionar_pregunta").prop("disabled", false);
                        $("#seleccionar_respuesta").prop("disabled", false);
                    }
                });

            }
        }

    } else {

        BootstrapDialog.show({
            title: "Crear Preguntas",
            closable: true,
            closeByBackdrop: false,
            closeByKeyboard: false,
            size: BootstrapDialog.SIZE_WIDE,
            message: "<div style=\"text-align: center;\">No es posible crear la pregunta mientras la encuesta esté vigente</div>",
            buttons: [{
                label: "Cerrar",
                cssClass: "btn",
                action: function (dialog) {
                    dialog.close();
                }
            }]
        });

    }
}

function crear_vista_rama(id_encuesta, id_pregunta, id_respuesta, tipo, op) {
    let ramaHtml = "";
    arrayRama = [];

    $.post(URL_MODULO + `controlador.php`, {
        accion: "buscar_ramificacion",
        id_encuesta: id_encuesta
    },

        function (data) {
            data = JSON.parse(data);

            recorrer_rama(data, id_pregunta, id_respuesta, id_pregunta, id_respuesta, 0, op);

            $.each(data, function (index, row) {
                ramaHtml += "<div style=\"text-align: center;float: left;font-weight: bold;\"><div style=\"font-size: 9px;\">" + row.titulo + "</div><span class=\"glyphicon glyphicon-list-alt\" style=\"color:#00638d\"></span></div>";
            });

            for (let i = arrayRama.length - 1; i >= 0; i--) {
                let style = "";

                if (i == 0)
                    style = "border: 1px solid #00638d;padding: 1px; margin-top: -4px;";

                if (arrayRama[i].pregunta != "")
                    ramaHtml += "<div style=\"float: left;position: relative;\"><span class=\"glyphicon glyphicon-arrow-right\" style=\"top: 12px;\"></span> <div style=\"text-align: center;float: right;" + style + "\"><div style=\"font-size: 9px;font-weight: bold;\">" + arrayRama[i].pregunta + "</div> <span class=\"glyphicon glyphicon-question-sign\" style=\"color:#00638d\"></span></div></div>";

                if (arrayRama[i].respuesta != "")
                    ramaHtml += "<div style=\"float: left;position: relative;\"><span class=\"glyphicon glyphicon-arrow-right\" style=\"top: 12px;\"></span> <div style=\"text-align: center;float: right;" + style + "\"><div style=\"font-size: 9px;font-weight: bold;\">" + arrayRama[i].respuesta + "</div> <span class=\"glyphicon glyphicon-check\" style=\"color:#00638d\"></span></div></div>";
            }

            if (op == 1)
                validar_rama(data, id_encuesta, id_pregunta, id_respuesta, id_respuesta, tipo, 0);

            $("#ramaEncuesta").html(ramaHtml);
            $("#ramaEncuesta2").html(ramaHtml);
        }
    );
}

function recorrer_rama(data, id_pregunta, id_respuesta, id_preguntaVar, id_respuestaVar, cont, op) {
    let bandSigue = 0;
    let preguntasArray = [];
    let respuestasArray = [];
    let id_preguntaResp = 0;
    let id_respuestaPre = 0;

    $.each(data, function (index, row) {
        $.each(row.preguntas, function (index, rowP) {
            $.each(rowP.respuestas, function (index, rowR) {
                respuestasArray.push(rowR);
            });

            preguntasArray.push(rowP);
        });
    });

    if (op == 1) {
        $.each(preguntasArray, function (index, preguntass) {

            if (preguntass.id_pregunta == id_preguntaVar) {

                arrayRama.push({ pregunta: preguntass.pregunta, respuesta: "" });

                if (preguntass.id_resp_sub > 0) {
                    id_respuestaPre = preguntass.id_resp_sub;
                    bandSigue = 1;
                }
            }
        });
    } else {
        $.each(respuestasArray, function (index, respuestass) {

            if (respuestass.id_respuesta == id_respuestaVar) {

                arrayRama.push({ pregunta: "", respuesta: respuestass.respuesta });

                id_preguntaResp = respuestass.id_pregunta;
                bandSigue = 1;
            }

        });
    }

    cont++;

    if (bandSigue) {
        recorrer_rama(data, id_pregunta, id_respuesta, id_preguntaResp, id_respuestaPre, cont);
    }

}

function validar_rama(data, id_encuesta, id_pregunta, id_respuesta, id_respuestaPa, tipo, cont) {
    let bandPre = 0;
    let select = "";
    let bandSigue = 0;
    let id_prePadre = 0;
    let id_respPadre = 0;

    $.each(data, function (index, row) {
        $.each(row.preguntas, function (index, rowP) {
            if (rowP.id_pregunta == id_pregunta) {
                $.each(rowP.respuestas, function (index, rowR) {
                    if (rowR.id_pregunta == rowP.id_pregunta) {
                        bandPre = 1;

                        if (rowR.cant_pre > 0 && rowR.id_respuesta != id_respuestaPa) {
                            bandSigue = 1;
                            id_prePadre = rowR.id_pregunta;
                            id_respPadre = rowR.id_respuesta;
                        }
                    }
                });
            }
        });
    });

    if (bandPre && cont == 0) {
        select += "<option value = \"\">Seleccionar...</option>";
        select += "<option value = \"1\">Única</option>";
        select += "<option value = \"2\">Múltiple</option>";

        $("#tipopc").html(select).change();
        $("#tipopc").val(tipo).prop("selected", true).change();
    }

    if (cont == 1) {
        select += "<option value = \"\">Seleccionar...</option>";
        select += "<option value = \"1\" selected >Única</option>";

        $("#tipopc").html(select).change();
    }

    if (!bandPre && cont == 0) {
        select += "<option value = \"\">Seleccionar...</option>";
        select += "<option value = \"1\">Única</option>";
        select += "<option value = \"2\">Múltiple</option>";
        select += "<option value = \"3\">Abierta Alfanumérica</option>";
        select += "<option value = \"4\">Abierta Numérica</option>";

        $("#tipopc").html(select).change();
        $("#tipopc").val(tipo).prop("selected", true).change();
    }

    cont++;

    if (bandSigue) {
        validar_rama(data, id_encuesta, id_prePadre, id_respPadre, id_respPadre, tipo, cont);
    }
}

function cargar_asignacion(id_enc) {
    // Oculta las asignaciones del paso 2
    $("#asig-paso2-distri, #asig-paso2-oper, #asig-paso2-sis").hide();

    /**
     * Recorre la etapa 1 de la asignación, para
     * restaurar los datos
     */
    for (let i in asignacion.etapa1) {
        if (asignacion.etapa1[i] !== true && asignacion.etapa1[i] !== false && checkJSON(asignacion.etapa1[i])) {
            for (let ii in asignacion.etapa1[i])
                asignacion.etapa1[i][ii] = false;
        } else
            asignacion.etapa1[i] = false;
    }

    /**
     * Recorre la etapa 2 de la asignación, para
     * restaurar los datos
     */
    for (let i in asignacion.etapa2) {
        if (asignacion.etapa2[i] !== true && asignacion.etapa2[i] !== false && checkJSON(asignacion.etapa2[i])) {
            for (let ii in asignacion.etapa2[i])
                asignacion.etapa2[i][ii] = false;
        } else
            asignacion.etapa2[i] = false;
    }

    /**
     * operador - app
     */
    $(".indi_asig_oper_app").unbind("click");
    $(".indi_asig_oper_app").click(() => {
        if ($(".indi_asig_oper_app").hasClass("glyphicon-plus")) {
            $(".indi_asig_oper_app").removeClass("glyphicon-plus");
            $(".indi_asig_oper_app").addClass("glyphicon-minus");
            $("#deta_asig_oper_app").slideDown("fast");
        } else {
            $(".indi_asig_oper_app").addClass("glyphicon-plus");
            $(".indi_asig_oper_app").removeClass("glyphicon-minus");
            $("#deta_asig_oper_app").slideUp("fast");
        }
    });

    if ($(".oper_app").hasClass("glyphicon-check")) {
        $(".oper_app").removeClass("glyphicon-check");
        $(".oper_app").addClass("glyphicon-unchecked");
    }

    $(".oper_app").unbind("click");
    $(".oper_app").click(() => {
        asignar_check(1.1);
    });

    $(".check_oper_app").unbind("click");
    $(".check_oper_app").click(event => {
        asignar_check_deta_app(1.1, undefined, undefined, event.currentTarget);
    });

    /**
     * distribuidor - app
     */
    $(".indi_app_pdv").unbind("click");
    $(".indi_app_pdv").click(function () {
        if ($(".indi_app_pdv").hasClass("glyphicon-plus")) {
            $(".indi_app_pdv").removeClass("glyphicon-plus");
            $(".indi_app_pdv").addClass("glyphicon-minus");
            $("#ver_deta_app_pdv").slideDown("fast");
        } else {
            $(".indi_app_pdv").addClass("glyphicon-plus");
            $(".indi_app_pdv").removeClass("glyphicon-minus");
            $("#ver_deta_app_pdv").slideUp("fast");
        }
    });

    if ($(".app-pdv").hasClass("glyphicon-check")) {
        $(".app-pdv").removeClass("glyphicon-check");
        $(".app-pdv").addClass("glyphicon-unchecked");
    }

    $(".app-pdv").unbind("click");
    $(".app-pdv").click(function () {
        asignar_check(2.1);
    });

    $("#ver_deta_app_pdv div i").unbind("click");
    $("#ver_deta_app_pdv div i").click(event => {
        asignar_check_deta_app(2.1, undefined, undefined, event.currentTarget);
    });

    /**
     * sistema (etapa 1)
     */
    $(".check_sistema").unbind("click");
    $(".check_sistema").click(() => {
        asignar_check(3.1);
    });

    /**
     * sistema (etapa 2) - operador
     */
    $("i.check_oper_paso2").unbind("click");
    $("i.check_oper_paso2").click(event => {
        asignar_check(4.1, event.currentTarget);
    });

    /**
     * sistema (etapa 2) - distribuidor
     */
    $("div#ver_deta_app div i").unbind("click");
    $("div#ver_deta_app div i").click(event => {
        asignar_check(5.1, event.currentTarget);
    });

    /**
     * sistema (etapa 2) - pdv
     */
    $("i.sis-pdv").unbind("click");
    $("i.sis-pdv").click(event => {
        asignar_check(6.1, event.currentTarget);
    });

    /**
     * asignación DCS (etapa 3)
     */
    $(".zonales_check").addClass("glyphicon-unchecked");

    datos_reg = [];
    datos_dis = [];
    datos_ter = [];
    datos_zonas = [];
    in_regional = [];
    in_dis = [];
    in_ter = [];
    in_zonas = [];

    cargar_dcs(id_enc);
}

/**
 * Asigna "check" individualmente o todos los elementos de la lista
 * @param option
 * @param element
 */
function asignar_check(option, element = undefined) {
    /**
     * etapa 1 - operador (app)
     */
    if (option === 1.1) {
        if ($("#deta_asig_oper_app div:first-child i").hasClass("glyphicon-unchecked")) {
            $("#deta_asig_oper_app div i").removeClass("glyphicon-unchecked");
            $("#deta_asig_oper_app div i").addClass("glyphicon-check");

            $(".oper_app").removeClass("glyphicon-unchecked");
            $(".oper_app").addClass("glyphicon-check");

            for (let i in asignacion.etapa1.operador)
                asignacion.etapa1.operador[i] = true;
        } else {
            $("#deta_asig_oper_app div i").removeClass("glyphicon-check");
            $("#deta_asig_oper_app div i").addClass("glyphicon-unchecked");

            $(".oper_app").addClass("glyphicon-unchecked");
            $(".oper_app").removeClass("glyphicon-check");

            for (let i in asignacion.etapa1.operador)
                asignacion.etapa1.operador[i] = false;
        }
    }

    /**
     * etapa 1 - distribuidor (app)
     */
    else if (option === 2.1) {
        if ($("#ver_deta_app_pdv div:first-child i").hasClass("glyphicon-unchecked")) {
            $("#ver_deta_app_pdv div i").removeClass("glyphicon-unchecked");
            $("#ver_deta_app_pdv div i").addClass("glyphicon-check");

            $(".app-pdv").removeClass("glyphicon-unchecked");
            $(".app-pdv").addClass("glyphicon-check");

            asignacion.etapa1.distribuidor = {
                vendedor: true,
                supervisor: true
            };
        } else {
            $("#ver_deta_app_pdv div i").removeClass("glyphicon-check");
            $("#ver_deta_app_pdv div i").addClass("glyphicon-unchecked");

            $(".app-pdv").removeClass("glyphicon-check");
            $(".app-pdv").addClass("glyphicon-unchecked");

            asignacion.etapa1.distribuidor = {
                vendedor: false,
                supervisor: false
            };
        }
    }

    /**
     * etapa 1 - sistema
     */
    else if (option === 3.1) {
        if ($(".check_sistema").hasClass("glyphicon-unchecked")) {
            asignacion.etapa1.sistema = true;

            $(".check_sistema").removeClass("glyphicon-unchecked");
            $(".check_sistema").addClass("glyphicon-check");
        } else {
            asignacion.etapa1.sistema = false;

            $(".check_sistema").removeClass("glyphicon-check");
            $(".check_sistema").addClass("glyphicon-unchecked");
        }
    }

    /**
     * etapa 2 - operador (web)
     */
    else if (option === 4.1) {
        if ($(element).hasClass("glyphicon-unchecked")) {
            $(element).removeClass("glyphicon-unchecked");
            $(element).addClass("glyphicon-check");

            asignacion.etapa2.operador[$(element).attr("id")] = true;
        } else {
            $(element).removeClass("glyphicon-check");
            $(element).addClass("glyphicon-unchecked");

            asignacion.etapa2.operador[$(element).attr("id")] = false;
        }
    }

    /**
     * etapa 2 - distribuidor (web)
     */
    else if (option === 5.1) {
        if ($(element).hasClass("glyphicon-unchecked")) {
            $(element).removeClass("glyphicon-unchecked");
            $(element).addClass("glyphicon-check");

            if ($(element).hasClass("app-usuario_sistema")) {
                asignacion.etapa2.distribuidor.usuario_sistema = true;
            } else if ($(element).hasClass("app-vende")) {
                asignacion.etapa2.distribuidor.vendedor = true;
            } else if ($(element).hasClass("app-super")) {
                asignacion.etapa2.distribuidor.supervisor = true;
            }
        } else {
            $(element).removeClass("glyphicon-check");
            $(element).addClass("glyphicon-unchecked");

            if ($(element).hasClass("app-usuario_sistema")) {
                asignacion.etapa2.distribuidor.usuario_sistema = false;
            } else if ($(element).hasClass("app-vende")) {
                asignacion.etapa2.distribuidor.vendedor = false;
            } else if ($(element).hasClass("app-super")) {
                asignacion.etapa2.distribuidor.supervisor = false;
            }
        }
    }

    /**
     * etapa 2 - pdv (web)
     */
    else if (option === 6.1) {
        if ($(element).hasClass("glyphicon-unchecked")) {
            $(element).removeClass("glyphicon-unchecked");
            $(element).addClass("glyphicon-check");

            asignacion.etapa2.pdv = true;
        } else {
            $(element).removeClass("glyphicon-check");
            $(element).addClass("glyphicon-unchecked");

            asignacion.etapa2.pdv = false;
        }
    }
}

/**
 * Asigna "check" a un elemento especifico de la lista
 * @param option
 * @param id_enc
 * @param second_option
 * @param element
 */
function asignar_check_deta_app(option, id_enc = undefined, second_option = undefined, element = undefined) {
    /**
     * operador (app)
     */
    if (option === 1.1) {
        if ($(".oper_app").hasClass("glyphicon-unchecked")) {
            $(".oper_app").removeClass("glyphicon-unchecked");
            $(".oper_app").addClass("glyphicon-check");
        }

        if ($(element).hasClass("glyphicon-unchecked")) {
            asignacion.etapa1.operador[$(element).attr("id")] = true;

            $(element).removeClass("glyphicon-unchecked");
            $(element).addClass("glyphicon-check");
        } else {
            asignacion.etapa1.operador[$(element).attr("id")] = false;

            $(element).removeClass("glyphicon-check");
            $(element).addClass("glyphicon-unchecked");
        }

        if (!$(".check_oper_app").hasClass("glyphicon-check")) {
            $(".oper_app").addClass("glyphicon-unchecked");
            $(".oper_app").removeClass("glyphicon-check");
        }
    }

    /**
     * distribuidor (app)
     */
    else if (option === 2.1) {
        if ($(".app-pdv").hasClass("glyphicon-unchecked")) {
            $(".app-pdv").removeClass("glyphicon-unchecked");
            $(".app-pdv").addClass("glyphicon-check");
        }

        if ($(element).hasClass("glyphicon-unchecked")) {
            asignacion.etapa1.distribuidor[$(element).attr("id")] = true;

            $(element).removeClass("glyphicon-unchecked");
            $(element).addClass("glyphicon-check");
        } else {
            asignacion.etapa1.distribuidor[$(element).attr("id")] = false;

            $(element).removeClass("glyphicon-check");
            $(element).addClass("glyphicon-unchecked");
        }

        if (!$("#ver_deta_app_pdv div i").hasClass("glyphicon-check")) {
            $(".app-pdv").addClass("glyphicon-unchecked");
            $(".app-pdv").removeClass("glyphicon-check");
        }
    }

    /**
     * distribuidor (sistema/web)
     */
    /*else if (second_option == 1) {
     if ($(".app").hasClass("glyphicon-check")) {
     if (option == 1) {
     if ($(".app-vende").hasClass("glyphicon-unchecked")) {
     $(".app-vende").removeClass("glyphicon-unchecked");
     $(".app-vende").addClass("glyphicon-check");
     } else {
     $(".app-vende").removeClass("glyphicon-check");
     $(".app-vende").addClass("glyphicon-unchecked");
     }
     }

     if (option == 2) {
     if ($(".app-super").hasClass("glyphicon-unchecked")) {
     $(".app-super").removeClass("glyphicon-unchecked");
     $(".app-super").addClass("glyphicon-check");
     } else {
     $(".app-super").removeClass("glyphicon-check");
     $(".app-super").addClass("glyphicon-unchecked");
     }
     }

     if (option == 3) {
     if ($(".app-usuario_sistema").hasClass("glyphicon-unchecked")) {
     $(".app-usuario_sistema").removeClass("glyphicon-unchecked");
     $(".app-usuario_sistema").addClass("glyphicon-check");
     } else {
     $(".app-usuario_sistema").removeClass("glyphicon-check");
     $(".app-usuario_sistema").addClass("glyphicon-unchecked");
     }
     }
     } else {

     Notificacion("Es necesario que selecciones la casilla App para realizar este procedimiento", "warning");

     }

     }*/
    else if (second_option == 2) {
        /*
         if ($(".app-pdv").hasClass("glyphicon-check")) {

         if (option == 1) {

         if ($(".app-vende-pdv").hasClass("glyphicon-unchecked")) {
         $(".app-vende-pdv").removeClass("glyphicon-unchecked");
         $(".app-vende-pdv").addClass("glyphicon-check");
         } else {
         $(".app-vende-pdv").removeClass("glyphicon-check");
         $(".app-vende-pdv").addClass("glyphicon-unchecked");
         }

         }

         if (option == 2) {

         if ($(".app-super-pdv").hasClass("glyphicon-unchecked")) {
         $(".app-super-pdv").removeClass("glyphicon-unchecked");
         $(".app-super-pdv").addClass("glyphicon-check");
         } else {
         $(".app-super-pdv").removeClass("glyphicon-check");
         $(".app-super-pdv").addClass("glyphicon-unchecked");
         }

         }

         } else {

         Notificacion("Es necesario que selecciones la casilla Pdv para realizar este procedimiento", "warning");

         }*/
    } else if (second_option == 3) {

    }

}

/**
 * Carga los permisos DCS asignados
 */
function cargar_dcs(id_enc) {
    fetch(`${URL_MODULO}/controlador.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: "cargar_dcs",
            id_enc: id_enc
        })
    }).then(response => response.json()).then(datos => {
        let regionales = "";

        datos_reg = datos["regio"];
        datos_dis = datos["distri"];
        datos_ter = datos["terri"];
        datos_zonas = datos["zonas"];
        niveles = datos["niveles"];
        operador = datos["operador"];

        /**
         * Paso 1 y paso 2
         */
        $.each(niveles, function (index, fila_nivel) {
            console.log(fila_nivel.id_encuesta)
            if (fila_nivel.id_encuesta == id_enc) {
                /**
                 * Paso 1 - distribuidor
                 */
                //alert(fila_nivel.nivel + " " + fila_nivel.app )
                if (fila_nivel.nivel == 4 && fila_nivel.vendedor == 1) {
                    $(".app-pdv").removeClass("glyphicon-unchecked");
                    $(".app-pdv").addClass("glyphicon-check");
                    asignacion.etapa1.distribuidor.vendedor = true;
                    $(".app-vende-pdv").removeClass("glyphicon-unchecked");
                    $(".app-vende-pdv").addClass("glyphicon-check");
                } else {
                    asignacion.etapa1.distribuidor.vendedor = false;
                }

                if (fila_nivel.nivel == 4 && fila_nivel.supervisor == 1) {
                    asignacion.etapa1.distribuidor.supervisor = true;

                    $(".app-pdv").removeClass("glyphicon-unchecked");
                    $(".app-pdv").addClass("glyphicon-check");
                    $(".app-super-pdv").removeClass("glyphicon-unchecked");
                    $(".app-super-pdv").addClass("glyphicon-check");
                } else {
                    asignacion.etapa1.distribuidor.supervisor = false;
                }

                /*if (fila_nivel.nivel == 2 && fila_nivel.app == 1 ) {
                    $(".app-pdv").removeClass("glyphicon-unchecked");
                    $(".app-pdv").addClass("glyphicon-check");

                    if (fila_nivel.nivel == 4 && fila_nivel.vendedor == 1) {
                        asignacion.etapa1.distribuidor.vendedor = true;

                        $(".app-vende-pdv").removeClass("glyphicon-unchecked");
                        $(".app-vende-pdv").addClass("glyphicon-check");
                    } else {

                        asignacion.etapa1.distribuidor.vendedor = false;
                    }

                    if (fila_nivel.nivel == 4 && fila_nivel.supervisor == 1) {
                        asignacion.etapa1.distribuidor.supervisor = true;

                        $(".app-super-pdv").removeClass("glyphicon-unchecked");
                        $(".app-super-pdv").addClass("glyphicon-check");
                    } else {
                        asignacion.etapa1.distribuidor.supervisor = false;
                    }
                }*/

                /**
                 * Paso 2 - distribuidor
                 */
                if (fila_nivel.nivel == 1) {
                    if (fila_nivel.web == 1) {
                        asignacion.etapa1.sistema = true;

                        $(".check_sistema").removeClass("glyphicon-unchecked");
                        $(".check_sistema").addClass("glyphicon-check");
                    }
                }

                console.log(fila_nivel)

                if (fila_nivel.nivel == 2 && fila_nivel.web == 1) {
                    // Usuario del sistema
                    if (fila_nivel.vendedor == 0 && fila_nivel.supervisor == 0) {
                        asignacion.etapa1.sistema = true;
                        asignacion.etapa2.distribuidor.usuario_sistema = true;

                        $(".app-usuario_sistema, .check_sistema").removeClass("glyphicon-unchecked");
                        $(".app-usuario_sistema, .check_sistema").addClass("glyphicon-check");
                    }


                }

                // Vendedor
                if (fila_nivel.nivel == 3 && fila_nivel.vendedor == 1) {
                    asignacion.etapa1.sistema = true;
                    asignacion.etapa2.distribuidor.vendedor = true;

                    $(".app-vende, .check_sistema").removeClass("glyphicon-unchecked");
                    $(".app-vende, .check_sistema").addClass("glyphicon-check");
                }

                // Supervisor
                if (fila_nivel.nivel == 3 && fila_nivel.supervisor == 1) {
                    asignacion.etapa1.sistema = true;
                    asignacion.etapa2.distribuidor.supervisor = true;

                    $(".app-super, .check_sistema").removeClass("glyphicon-unchecked");
                    $(".app-super, .check_sistema").addClass("glyphicon-check");
                }
                if (fila_nivel.nivel == 6) {
                    asignacion.etapa1.sistema = true;
                    asignacion.etapa2.pdv = true;

                    $(".check_sistema").removeClass("glyphicon-unchecked");
                    $(".check_sistema").addClass("glyphicon-check");
                    $(".sis-pdv").removeClass("glyphicon-unchecked");
                    $(".sis-pdv").addClass("glyphicon-check");
                }
            }
        });

        $.each(operador, (key, item) => {
            // Operador (sistema/web)
            if (item.tipo == "0") {
                $("#item_oper_paso2_" + item.id_perfil).removeClass("glyphicon-unchecked");
                $("#item_oper_paso2_" + item.id_perfil).addClass("glyphicon-check");

                asignacion.etapa2.operador["item_oper_paso2_" + item.id_perfil] = true;
            }

            // Operador (app)
            if (item.tipo == "1") {
                if ($(".oper_app").hasClass("glyphicon-unchecked")) {
                    $(".oper_app").removeClass("glyphicon-unchecked");
                    $(".oper_app").addClass("glyphicon-check");
                }

                $("#item_oper_app_" + item.id_perfil).removeClass("glyphicon-unchecked");
                $("#item_oper_app_" + item.id_perfil).addClass("glyphicon-check");

                asignacion.etapa1.operador["item_oper_app_" + item.id_perfil] = true;
            }
        });

        /**
         * Paso 3 - asignación DCS
         */
        $.each(datos_reg, function (index, fila_reg) {
            let class_check = "glyphicon-unchecked";
            let permiso_reg = parseInt(fila_reg.permiso);
            let distribuidores = "";
            let texto = "";

            if (permiso_reg == 0) {
                class_check = "glyphicon-check";
                in_regional.push({ id_reg: fila_reg.id_reg, op: "regio" });
                texto = "Toda la zonal";
            }

            regionales += "<div style='padding-bottom: 5px;'>";
            regionales += "<div style='float: left;'>";
            regionales += "<i class='glyphicon " + class_check + "' id = 's_reg_" + fila_reg.id_reg + "' onclick='check_all(" + fila_reg.id_reg + ",0,0,1)' style='font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;'></i> ";
            regionales += fila_reg.nom_reg + " <span style='color: #10628a;font-weight: bold;' id='permisos_" + fila_reg.id_reg + "'>" + texto + "<span>";
            regionales += "</div>";
            regionales += "<div style='position:absolute;cursor: pointer;right: 0;'><i class='glyphicon glyphicon-plus' id = 'ver_mas_dis_" + fila_reg.id_reg + "' Onclick='ver_mas(" + fila_reg.id_reg + ",0,0,1)' style='font-size: 12px;color: #10628a;position: relative;top: -2px;cursor:pointer;'></i></div>";
            regionales += "</div><br>";

            distribuidores += "<div style='display:none;padding: 5px 5px 5px 10px;border-left: 1px solid #c0c0c0;' id = 'ver_dis_" + fila_reg.id_reg + "'>";
            distribuidores += "<div style='border-bottom: 1px solid #c0c0c0;padding-bottom: 5px;'>";
            distribuidores += "<i class=\"glyphicon " + class_check + "\" id = \"all_dis_" + fila_reg.id_reg + "\" Onclick=\"check_all2(" + fila_reg.id_reg + ",0,1)\" style=\"font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;\"></i> ";
            distribuidores += "<span style=\"font-weight: bold;font-size: 15px;\">Agentes</span>";
            distribuidores += "</div>";

            $.each(datos_dis, function (index, fila_dis) {
                if (fila_dis.id_reg == fila_reg.id_reg) {
                    let class_check = "glyphicon-unchecked";
                    let permiso_dis = parseInt(fila_dis.permiso);
                    let territorio = "";

                    if (permiso_dis == 0 || permiso_reg == 0) {
                        class_check = "glyphicon-check";

                        in_dis.push({
                            id_reg: fila_dis.id_reg,
                            id_dis: fila_dis.id,
                            op: "distri"
                        });
                    }

                    distribuidores += "<div style='padding-top: 5px;'>";
                    distribuidores += "<div style='float: left;'>";
                    distribuidores += "<i class='glyphicon " + class_check + "' id = 's_dis_" + fila_dis.id + "_" + fila_dis.id_reg + "' Onclick='check_all(" + fila_dis.id + "," + fila_dis.id_reg + ",0,2)' style='font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;'></i> ";
                    distribuidores += fila_dis.nombre;
                    distribuidores += "</div>";
                    distribuidores += "<div style='position:absolute;cursor: pointer;right: 10px;'><i class='glyphicon glyphicon-plus' id = 'ver_mas_ter_" + fila_dis.id + "_" + fila_dis.id_reg + "' Onclick='ver_mas(" + fila_dis.id + "," + fila_dis.id_reg + ",0,2)' style='font-size: 12px;color: #10628a;position: relative;top: -2px;cursor:pointer;'></i></div>";
                    distribuidores += "</div><br>";

                    territorio += "<div style='display:none;position: relative;padding: 5px 5px 5px 10px;border-left: 1px solid #c0c0c0;' id = 'ver_ter_" + fila_dis.id + "_" + fila_dis.id_reg + "'>";
                    territorio += "<div style='border-bottom: 1px solid #c0c0c0;padding-bottom: 5px;'>";
                    territorio += "<i class=\"glyphicon " + class_check + "\" id = \"all_ter_" + fila_dis.id + "_" + fila_dis.id_reg + "\" Onclick=\"check_all2(" + fila_dis.id + "," + fila_dis.id_reg + ",2)\" style=\"font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;\"></i> ";
                    territorio += "<span style=\"font-weight: bold;font-size: 15px;\">Rutas</span>";
                    territorio += "</div>";

                    $.each(datos_ter, function (index, fila_ter) {
                        if (fila_ter.id_distri == fila_dis.id && fila_ter.id_regional == fila_reg.id_reg) {
                            let class_check = "glyphicon-unchecked";
                            let permiso_ter = parseInt(fila_ter.permiso);
                            let zonas = "";

                            if (permiso_ter == 0 || permiso_dis == 0 || permiso_reg == 0) {
                                class_check = "glyphicon-check";

                                in_ter.push({
                                    id_reg: fila_ter.id_regional,
                                    id_dis: fila_ter.id_distri,
                                    id_terri: fila_ter.id,
                                    op: "terri"
                                });
                            }

                            territorio += "<div style='padding-top: 5px;'>";
                            territorio += "<div style='float: left;'>";
                            territorio += "<i class='glyphicon " + class_check + "' id = 's_ter_" + fila_ter.id + "' Onclick='check_all(" + fila_ter.id + "," + fila_ter.id_regional + "," + fila_ter.id_distri + ",3)' style='font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;'></i> ";
                            territorio += fila_ter.nombre;
                            territorio += "</div>";
                            territorio += "<div style='position:absolute;cursor: pointer;right: 5px;'><i class='glyphicon glyphicon-plus' id = 'ver_mas_zona_" + fila_ter.id + "_" + fila_ter.id_regional + "_" + fila_ter.id_distri + "' Onclick='ver_mas(" + fila_ter.id + "," + fila_ter.id_regional + "," + fila_ter.id_distri + ",3)' style='font-size: 12px;color: #10628a;position: relative;top: -2px;cursor:pointer;'></i></div>";
                            territorio += "</div><br>";

                            zonas += "<div style='display:none;position: relative;padding: 5px 5px 5px 10px;border-left: 1px solid #c0c0c0;' id = 'ver_zona_" + fila_ter.id + "_" + fila_ter.id_regional + "_" + fila_ter.id_distri + "'>";
                            zonas += "<div style='border-bottom: 1px solid #c0c0c0;padding-bottom: 5px;'>";
                            zonas += "<i class=\"glyphicon " + class_check + "\" id = \"all_zonas_" + fila_ter.id + "\" Onclick=\"check_all2(" + fila_ter.id + ",0,3)\" style=\"font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;\"></i> ";
                            zonas += "<span style=\"font-weight: bold;font-size: 15px;\">Circuitos</span>";
                            zonas += "</div>";

                            $.each(datos_zonas, function (index, fila_zona) {
                                if (fila_zona.territorio == fila_ter.id) {
                                    let class_check = "glyphicon-unchecked";
                                    let permiso_zona = parseInt(fila_zona.permiso);

                                    if (permiso_zona == 0 || permiso_ter == 0 || permiso_dis == 0 || permiso_reg == 0) {
                                        class_check = "glyphicon-check";

                                        in_zonas.push({
                                            id_reg: fila_zona.id_regional,
                                            id_dis: fila_zona.id_distri,
                                            id_terri: fila_zona.territorio,
                                            id_zona: fila_zona.id,
                                            op: "zonas"
                                        });
                                    }

                                    zonas += "<div style='padding-top: 5px;'>";
                                    zonas += "<div style='float: left;'>";
                                    zonas += "<i class='glyphicon " + class_check + "' id = 's_zona_" + fila_zona.id + "' Onclick='check_rutas(" + fila_zona.id_regional + "," + fila_zona.id_distri + "," + fila_zona.territorio + "," + fila_zona.id + ")' style='font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;'></i> ";
                                    zonas += fila_zona.nombre;
                                    zonas += "</div>";
                                    zonas += "</div><br>";
                                }
                            });

                            zonas += "</div>";
                            territorio += zonas;
                        }
                    });

                    territorio += "</div>";

                    distribuidores += territorio;
                }
            });

            distribuidores += "</div>";

            regionales += distribuidores;
        });

        $("#contenido_dcs").html(regionales);

        $.each(datos_dis, function (index, fila_dis) {
            permiso = parseInt(fila_dis.permiso);

            if (permiso == 0) {
                $("#s_reg_" + fila_dis.id_reg).removeClass("glyphicon-check");
                $("#s_reg_" + fila_dis.id_reg).addClass("glyphicon-unchecked");
                $("#s_reg_" + fila_dis.id_reg).css("color", "rgb(173, 142, 31)");
                $("#permisos_" + fila_dis.id_reg).html("Permiso agentes");
            }
        });

        $.each(datos_ter, function (index, fila_ter) {
            permiso = parseInt(fila_ter.permiso);

            if (permiso == 0) {
                $("#s_reg_" + fila_ter.id_regional).removeClass("glyphicon-check");
                $("#s_reg_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                $("#s_reg_" + fila_ter.id_regional).css("color", "rgb(173, 142, 31)");
                $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).removeClass("glyphicon-check");
                $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).css("color", "rgb(173, 142, 31)");
                $("#permisos_" + fila_ter.id_regional).html("Permiso ruta");
            }
        });

        $.each(datos_zonas, function (index, fila_zona) {
            permiso = parseInt(fila_zona.permiso);

            if (permiso == 0) {
                $("#s_reg_" + fila_zona.id_regional).removeClass("glyphicon-check");
                $("#s_reg_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                $("#s_reg_" + fila_zona.id_regional).css("color", "rgb(173, 142, 31)");
                $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).removeClass("glyphicon-check");
                $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).css("color", "rgb(173, 142, 31)");
                $("#s_ter_" + fila_zona.territorio).removeClass("glyphicon-check");
                $("#s_ter_" + fila_zona.territorio).addClass("glyphicon-unchecked");
                $("#s_ter_" + fila_zona.territorio).css("color", "rgb(173, 142, 31)");
                $("#permisos_" + fila_zona.id_regional).html("Permiso circuito");
            }
        });

        check_all_regio();
    }).catch(error => {
        console.error('Error:', error);
    });

    $("#guardar_asignacion").unbind("click");
    $("#guardar_asignacion").click(function () {
        guardar_asignacion(id_enc);
    });
}

function ver_mas(id, id2, id3, op) {
    if (op == 1) {
        if ($("#ver_mas_dis_" + id).hasClass("glyphicon-plus")) {
            $("#ver_mas_dis_" + id).removeClass("glyphicon-plus");
            $("#ver_mas_dis_" + id).addClass("glyphicon-minus");
            $("#ver_dis_" + id).slideDown("fast");
        } else {
            $("#ver_mas_dis_" + id).addClass("glyphicon-plus");
            $("#ver_mas_dis_" + id).removeClass("glyphicon-minus");
            $("#ver_dis_" + id).slideUp("fast");
        }
    } else if (op == 2) {
        if ($("#ver_mas_ter_" + id + "_" + id2).hasClass("glyphicon-plus")) {
            $("#ver_mas_ter_" + id + "_" + id2).removeClass("glyphicon-plus");
            $("#ver_mas_ter_" + id + "_" + id2).addClass("glyphicon-minus");
            $("#ver_ter_" + id + "_" + id2).slideDown("fast");
        } else {
            $("#ver_mas_ter_" + id + "_" + id2).addClass("glyphicon-plus");
            $("#ver_mas_ter_" + id + "_" + id2).removeClass("glyphicon-minus");
            $("#ver_ter_" + id + "_" + id2).slideUp("fast");
        }
    } else if (op == 3) {
        if ($("#ver_mas_zona_" + id + "_" + id2 + "_" + id3).hasClass("glyphicon-plus")) {
            $("#ver_mas_zona_" + id + "_" + id2 + "_" + id3).removeClass("glyphicon-plus");
            $("#ver_mas_zona_" + id + "_" + id2 + "_" + id3).addClass("glyphicon-minus");
            $("#ver_zona_" + id + "_" + id2 + "_" + id3).slideDown("fast");
        } else {
            $("#ver_mas_zona_" + id + "_" + id2 + "_" + id3).addClass("glyphicon-plus");
            $("#ver_mas_zona_" + id + "_" + id2 + "_" + id3).removeClass("glyphicon-minus");
            $("#ver_zona_" + id + "_" + id2 + "_" + id3).slideUp("fast");
        }
    }
}

function check_all_regio() {
    $(".zonales_check").unbind("click");
    $(".zonales_check").click(function () {

        if ($(this).hasClass("glyphicon-unchecked")) {
            $(this).removeClass("glyphicon-unchecked");
            $(this).addClass("glyphicon-check");

            $.each(datos_reg, function (index, fila_reg) {
                $("#s_reg_" + fila_reg.id_reg).removeClass("glyphicon-unchecked");
                $("#s_reg_" + fila_reg.id_reg).addClass("glyphicon-check");
                $("#all_dis_" + fila_reg.id_reg).removeClass("glyphicon-unchecked");
                $("#all_dis_" + fila_reg.id_reg).addClass("glyphicon-check");

                in_regional.push({ id_reg: fila_reg.id_reg, op: "regio" });
            });

            $.each(datos_dis, function (index, fila_dis) {
                $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-unchecked");
                $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-check");
                $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-unchecked");
                $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-check");
            });

            $.each(datos_ter, function (index, fila_ter) {
                $("#s_ter_" + fila_ter.id).removeClass("glyphicon-unchecked");
                $("#s_ter_" + fila_ter.id).addClass("glyphicon-check");
                $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-unchecked");
                $("#all_zonas_" + fila_ter.id).addClass("glyphicon-check");
            });

            $.each(datos_zonas, function (index, fila_zona) {
                $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");
            });
        } else {
            $(this).removeClass("glyphicon-check");
            $(this).addClass("glyphicon-unchecked");

            in_regional = [];
            in_dis = [];
            in_ter = [];
            in_zonas = [];

            $.each(datos_reg, function (index, fila_reg) {
                $("#s_reg_" + fila_reg.id_reg).removeClass("glyphicon-check");
                $("#s_reg_" + fila_reg.id_reg).addClass("glyphicon-unchecked");
                $("#all_dis_" + fila_reg.id_reg).removeClass("glyphicon-check");
                $("#all_dis_" + fila_reg.id_reg).addClass("glyphicon-unchecked");
            });

            $.each(datos_dis, function (index, fila_dis) {
                $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-check");
                $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-unchecked");
                $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-check");
                $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-unchecked");
            });

            $.each(datos_ter, function (index, fila_ter) {
                $("#s_ter_" + fila_ter.id).removeClass("glyphicon-check");
                $("#s_ter_" + fila_ter.id).addClass("glyphicon-unchecked");
                $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-check");
                $("#all_zonas_" + fila_ter.id).addClass("glyphicon-unchecked");
            });

            $.each(datos_zonas, function (index, fila_zona) {
                $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");
            });
        }
    });
}

function check_all(id, id2, id3, op) {
    if ($(".zonales_check").hasClass("glyphicon-check")) {
        $(".zonales_check").removeClass("glyphicon-check");
        $(".zonales_check").addClass("glyphicon-unchecked");
    }

    if (op == 1) {
        if ($("#s_reg_" + id).hasClass("glyphicon-unchecked")) {
            $("#s_reg_" + id).removeClass("glyphicon-unchecked");
            $("#s_reg_" + id).addClass("glyphicon-check");
            $("#all_dis_" + id).removeClass("glyphicon-unchecked");
            $("#all_dis_" + id).addClass("glyphicon-check");

            $.each(datos_dis, function (index, fila_dis) {
                if (fila_dis.id_reg == id) {
                    $("#s_dis_" + fila_dis.id + "_" + id).removeClass("glyphicon-unchecked");
                    $("#s_dis_" + fila_dis.id + "_" + id).addClass("glyphicon-check");
                    $("#all_ter_" + fila_dis.id + "_" + id).removeClass("glyphicon-unchecked");
                    $("#all_ter_" + fila_dis.id + "_" + id).addClass("glyphicon-check");

                    $.each(datos_ter, function (index, fila_ter) {
                        if (fila_ter.id_distri == fila_dis.id && fila_ter.id_regional == id) {
                            $("#s_ter_" + fila_ter.id).removeClass("glyphicon-unchecked");
                            $("#s_ter_" + fila_ter.id).addClass("glyphicon-check");
                            $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-unchecked");
                            $("#all_zonas_" + fila_ter.id).addClass("glyphicon-check");

                            $.each(datos_zonas, function (index, fila_zona) {
                                if (fila_zona.territorio == fila_ter.id) {
                                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");
                                }
                            });
                        }
                    });
                }
            });

            in_regional.push({ id_reg: id, op: "regio" });
        } else {
            $("#s_reg_" + id).removeClass("glyphicon-check");
            $("#s_reg_" + id).addClass("glyphicon-unchecked");
            $("#all_dis_" + id).removeClass("glyphicon-check");
            $("#all_dis_" + id).addClass("glyphicon-unchecked");

            $.each(datos_dis, function (index, fila_dis) {
                if (fila_dis.id_reg == id) {
                    $("#s_dis_" + fila_dis.id + "_" + id).removeClass("glyphicon-check");
                    $("#s_dis_" + fila_dis.id + "_" + id).addClass("glyphicon-unchecked");
                    $("#all_ter_" + fila_dis.id + "_" + id).removeClass("glyphicon-check");
                    $("#all_ter_" + fila_dis.id + "_" + id).addClass("glyphicon-unchecked");

                    $.each(datos_ter, function (index, fila_ter) {
                        if (fila_ter.id_distri == fila_dis.id && fila_ter.id_regional == id) {
                            $("#s_ter_" + fila_ter.id).removeClass("glyphicon-check");
                            $("#s_ter_" + fila_ter.id).addClass("glyphicon-unchecked");
                            $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-check");
                            $("#all_zonas_" + fila_ter.id).addClass("glyphicon-unchecked");

                            $.each(datos_zonas, function (index, fila_zona) {
                                if (fila_zona.territorio == fila_ter.id) {
                                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");
                                }
                            });
                        }
                    });

                    if ($("#all_dis_" + fila_dis.id_regional).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_dis.id_regional).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_dis.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_dis.id_regional).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_dis.id_regional).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_dis.id_regional).addClass("glyphicon-unchecked");
                    }
                }
            });

            $.each(in_regional, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id)
                        delete in_regional[index];
                }
            });

            $.each(in_dis, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id)
                        delete in_dis[index];
                }
            });

            $.each(in_ter, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id)
                        delete in_ter[index];
                }
            });

            $.each(in_zonas, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id)
                        delete in_zonas[index];
                }
            });
        }
    } else if (op == 2) {
        if ($("#s_dis_" + id + "_" + id2).hasClass("glyphicon-unchecked")) {
            $("#s_dis_" + id + "_" + id2).removeClass("glyphicon-unchecked");
            $("#s_dis_" + id + "_" + id2).addClass("glyphicon-check");
            $("#all_ter_" + id + "_" + id2).removeClass("glyphicon-unchecked");
            $("#all_ter_" + id + "_" + id2).addClass("glyphicon-check");

            $.each(datos_ter, function (index, fila_ter) {
                if (fila_ter.id_distri == id && fila_ter.id_regional == id2) {
                    $("#s_ter_" + fila_ter.id).removeClass("glyphicon-unchecked");
                    $("#s_ter_" + fila_ter.id).addClass("glyphicon-check");
                    $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-unchecked");
                    $("#all_zonas_" + fila_ter.id).addClass("glyphicon-check");

                    $.each(datos_zonas, function (index, fila_zona) {
                        if (fila_zona.territorio == fila_ter.id) {
                            $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                            $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");
                        }
                    });
                }
            });

            in_dis.push({ id_reg: id2, id_dis: id, op: "distri" });
        } else {
            $("#s_dis_" + id + "_" + id2).removeClass("glyphicon-check");
            $("#s_dis_" + id + "_" + id2).addClass("glyphicon-unchecked");
            $("#all_ter_" + id + "_" + id2).removeClass("glyphicon-check");
            $("#all_ter_" + id + "_" + id2).addClass("glyphicon-unchecked");

            $.each(datos_ter, function (index, fila_ter) {
                if (fila_ter.id_distri == id && fila_ter.id_regional == id2) {
                    $("#s_ter_" + fila_ter.id).removeClass("glyphicon-check");
                    $("#s_ter_" + fila_ter.id).addClass("glyphicon-unchecked");
                    $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-check");
                    $("#all_zonas_" + fila_ter.id).addClass("glyphicon-unchecked");

                    $.each(datos_zonas, function (index, fila_zona) {
                        if (fila_zona.territorio == fila_ter.id) {
                            $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                            $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");
                        }
                    });

                    if ($("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#all_dis_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }
                }
            });

            $.each(in_regional, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id2)
                        delete in_regional[index];
                }
            });

            $.each(in_dis, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_dis == id && fila.id_reg == id2)
                        delete in_dis[index];
                }
            });

            $.each(in_ter, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_dis == id && fila.id_reg == id2)
                        delete in_ter[index];
                }
            });

            $.each(in_zonas, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_dis == id && fila.id_reg == id2)
                        delete in_zonas[index];
                }
            });
        }
    } else if (op == 3) {
        if ($("#s_ter_" + id).hasClass("glyphicon-unchecked")) {
            $("#s_ter_" + id).removeClass("glyphicon-unchecked");
            $("#s_ter_" + id).addClass("glyphicon-check");
            $("#all_zonas_" + id).removeClass("glyphicon-unchecked");
            $("#all_zonas_" + id).addClass("glyphicon-check");

            $.each(datos_zonas, function (index, fila_zona) {
                if (fila_zona.territorio == id) {
                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");
                }
            });

            in_ter.push({ id_reg: id2, id_dis: id3, id_terri: id, op: "terri" });
        } else {
            $("#s_ter_" + id).removeClass("glyphicon-check");
            $("#s_ter_" + id).addClass("glyphicon-unchecked");
            $("#all_zonas_" + id).removeClass("glyphicon-check");
            $("#all_zonas_" + id).addClass("glyphicon-unchecked");

            $.each(datos_zonas, function (index, fila_zona) {
                if (fila_zona.territorio == id) {
                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");

                    if ($("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#all_dis_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    $.each(in_regional, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_reg == id2)
                                delete in_regional[index];
                        }
                    });

                    $.each(in_dis, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == id3 && fila.id_reg == id2)
                                delete in_dis[index];
                        }
                    });

                    $.each(in_ter, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_terri == id && fila.id_reg == id2 && fila.id_dis == id3)
                                delete in_ter[index];
                        }
                    });

                    $.each(in_zonas, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_terri == id && fila.id_reg == id2 && fila.id_dis == id3)
                                delete in_zonas[index];
                        }
                    });
                }
            });

            $.each(in_regional, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_reg == id2)
                        delete in_regional[index];
                }
            });

            $.each(in_dis, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_dis == id3 && fila.id_reg == id2)
                        delete in_dis[index];
                }
            });

            $.each(in_ter, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_terri == id && fila.id_reg == id2 && fila.id_dis == id3)
                        delete in_ter[index];
                }
            });

            $.each(in_zonas, function (index, fila) {
                if (fila !== undefined) {
                    if (fila.id_terri == id && fila.id_reg == id2 && fila.id_dis == id3)
                        delete in_zonas[index];
                }
            });
        }
    }
}

function check_rutas(id_reg, id_distri, id_terri, id) {
    if ($(".zonales_check").hasClass("glyphicon-check")) {
        $(".zonales_check").removeClass("glyphicon-check");
        $(".zonales_check").addClass("glyphicon-unchecked");
    }

    if ($("#s_zona_" + id).hasClass("glyphicon-unchecked")) {
        $("#s_zona_" + id).removeClass("glyphicon-unchecked");
        $("#s_zona_" + id).addClass("glyphicon-check");

        in_zonas.push({ id_reg: id_reg, id_dis: id_distri, id_terri: id_terri, id_zona: id, op: "zonas" });
    } else {
        $("#s_zona_" + id).removeClass("glyphicon-check");
        $("#s_zona_" + id).addClass("glyphicon-unchecked");

        if ($("#s_ter_" + id_terri).hasClass("glyphicon-check")) {
            $("#s_ter_" + id_terri).removeClass("glyphicon-check");
            $("#s_ter_" + id_terri).addClass("glyphicon-unchecked");
        }

        if ($("#all_zonas_" + id_terri).hasClass("glyphicon-check")) {
            $("#all_zonas_" + id_terri).removeClass("glyphicon-check");
            $("#all_zonas_" + id_terri).addClass("glyphicon-unchecked");
        }

        if ($("#all_ter_" + id_distri + "_" + id_reg).hasClass("glyphicon-check")) {
            $("#all_ter_" + id_distri + "_" + id_reg).removeClass("glyphicon-check");
            $("#all_ter_" + id_distri + "_" + id_reg).addClass("glyphicon-unchecked");
        }

        if ($("#s_dis_" + id_distri + "_" + id_reg).hasClass("glyphicon-check")) {
            $("#s_dis_" + id_distri + "_" + id_reg).removeClass("glyphicon-check");
            $("#s_dis_" + id_distri + "_" + id_reg).addClass("glyphicon-unchecked");
        }

        if ($("#all_dis_" + id_reg).hasClass("glyphicon-check")) {
            $("#all_dis_" + id_reg).removeClass("glyphicon-check");
            $("#all_dis_" + id_reg).addClass("glyphicon-unchecked");
        }

        if ($("#s_reg_" + id_reg).hasClass("glyphicon-check")) {
            $("#s_reg_" + id_reg).removeClass("glyphicon-check");
            $("#s_reg_" + id_reg).addClass("glyphicon-unchecked");
        }

        $.each(in_regional, function (index, fila) {
            if (fila !== undefined) {
                if (fila.id_reg == id_reg)
                    delete in_regional[index];
            }
        });

        $.each(in_dis, function (index, fila) {
            if (fila !== undefined) {
                if (fila.id_dis == id_distri && fila.id_reg == id_reg)
                    delete in_dis[index];
            }
        });

        $.each(in_ter, function (index, fila) {
            if (fila !== undefined) {
                if (fila.id_terri == id_terri && fila.id_reg == id_reg && fila.id_dis == id_distri)
                    delete in_ter[index];
            }
        });

        $.each(in_zonas, function (index, fila) {
            if (fila !== undefined) {
                if (fila.id_zona == id && fila.id_terri == id_terri && fila.id_reg == id_reg && fila.id_dis == id_distri)
                    delete in_zonas[index];
            }
        });
    }
}

function check_all2(id, id2, op) {

    if ($(".zonales_check").hasClass("glyphicon-check")) {
        $(".zonales_check").removeClass("glyphicon-check");
        $(".zonales_check").addClass("glyphicon-unchecked");
    }

    if (op == 1) {

        if ($("#all_dis_" + id).hasClass("glyphicon-unchecked")) {

            $("#all_dis_" + id).removeClass("glyphicon-unchecked");
            $("#all_dis_" + id).addClass("glyphicon-check");

            $.each(datos_dis, function (index, fila_dis) {

                if (fila_dis.id_reg == id) {

                    $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-unchecked");
                    $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-check");

                    $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-unchecked");
                    $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-check");

                    $.each(datos_ter, function (index, fila_ter) {

                        if (fila_ter.id_distri == fila_dis.id && fila_ter.id_regional == id) {

                            $("#s_ter_" + fila_ter.id).removeClass("glyphicon-unchecked");
                            $("#s_ter_" + fila_ter.id).addClass("glyphicon-check");

                            $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-unchecked");
                            $("#all_zonas_" + fila_ter.id).addClass("glyphicon-check");

                            $.each(datos_zonas, function (index, fila_zona) {

                                if (fila_zona.territorio == fila_ter.id) {

                                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");

                                }

                            });

                        }

                    });

                    in_dis.push({ id_reg: fila_dis.id_reg, id_dis: fila_dis.id, op: "distri" });

                    //in_dis[fila_dis.id_reg+fila_dis.id] = {id_reg:fila_dis.id_reg, id_dis:fila_dis.id,op:"distri"};

                }

            });

        } else {

            $("#all_dis_" + id).removeClass("glyphicon-check");
            $("#all_dis_" + id).addClass("glyphicon-unchecked");

            $.each(datos_dis, function (index, fila_dis) {

                if (fila_dis.id_reg == id) {

                    $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-check");
                    $("#s_dis_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-unchecked");

                    $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).removeClass("glyphicon-check");
                    $("#all_ter_" + fila_dis.id + "_" + fila_dis.id_reg).addClass("glyphicon-unchecked");

                    $.each(datos_ter, function (index, fila_ter) {

                        if (fila_ter.id_distri == fila_dis.id && fila_ter.id_regional == id) {

                            $("#s_ter_" + fila_ter.id).removeClass("glyphicon-check");
                            $("#s_ter_" + fila_ter.id).addClass("glyphicon-unchecked");

                            $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-check");
                            $("#all_zonas_" + fila_ter.id).addClass("glyphicon-unchecked");

                            $.each(datos_zonas, function (index, fila_zona) {

                                if (fila_zona.territorio == fila_ter.id) {

                                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");

                                }

                            });

                        }

                    });

                    if ($("#all_dis_" + fila_dis.id_reg).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_dis.id_reg).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_dis.id_reg).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_dis.id_reg).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_dis.id_reg).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_dis.id_reg).addClass("glyphicon-unchecked");
                    }

                    $.each(in_regional, function (index, fila) {

                        if (fila !== undefined) {
                            if (fila.id_reg == fila_dis.id_reg) {
                                delete in_regional[index];
                            }
                        }

                    });

                    $.each(in_dis, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == fila_dis.id) {
                                delete in_dis[index];
                            }
                        }

                    });

                    $.each(in_ter, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == fila_dis.id) {
                                delete in_ter[index];
                            }
                        }

                    });

                    $.each(in_zonas, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == fila_dis.id) {
                                delete in_zonas[index];
                            }
                        }

                    });

                }

            });

        }

    } else if (op == 2) {
        if ($("#all_ter_" + id + "_" + id2).hasClass("glyphicon-unchecked")) {

            $("#all_ter_" + id + "_" + id2).removeClass("glyphicon-unchecked");
            $("#all_ter_" + id + "_" + id2).addClass("glyphicon-check");

            $.each(datos_ter, function (index, fila_ter) {

                if (fila_ter.id_distri == id && fila_ter.id_regional == id2) {

                    $("#s_ter_" + fila_ter.id).removeClass("glyphicon-unchecked");
                    $("#s_ter_" + fila_ter.id).addClass("glyphicon-check");

                    $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-unchecked");
                    $("#all_zonas_" + fila_ter.id).addClass("glyphicon-check");

                    $.each(datos_zonas, function (index, fila_zona) {

                        if (fila_zona.territorio == fila_ter.id) {

                            $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                            $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");

                        }

                    });

                    in_ter.push({ id_reg: fila_ter.id_regional, id_dis: fila_ter.id_distri, id_terri: fila_ter.id, op: "terri" });

                    //in_ter[fila_ter.id_regional+fila_ter.id_distri+fila_ter.id] = {id_reg:fila_ter.id_regional, id_dis:fila_ter.id_distri, id_terri:fila_ter.id,op:"terri"};

                }

            });

        } else {

            $("#all_ter_" + id + "_" + id2).removeClass("glyphicon-check");
            $("#all_ter_" + id + "_" + id2).addClass("glyphicon-unchecked");

            $.each(datos_ter, function (index, fila_ter) {

                if (fila_ter.id_distri == id && fila_ter.id_regional == id2) {

                    $("#s_ter_" + fila_ter.id).removeClass("glyphicon-check");
                    $("#s_ter_" + fila_ter.id).addClass("glyphicon-unchecked");

                    $("#all_zonas_" + fila_ter.id).removeClass("glyphicon-check");
                    $("#all_zonas_" + fila_ter.id).addClass("glyphicon-unchecked");

                    $.each(datos_zonas, function (index, fila_zona) {

                        if (fila_zona.territorio == fila_ter.id) {

                            $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                            $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");

                        }

                    });

                    if ($("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#s_dis_" + fila_ter.id_distri + "_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#all_dis_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_ter.id_regional).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_ter.id_regional).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_ter.id_regional).addClass("glyphicon-unchecked");
                    }

                    $.each(in_regional, function (index, fila) {

                        if (fila !== undefined) {
                            if (fila.id_reg == fila_ter.id_regional) {
                                delete in_regional[index];
                            }
                        }

                    });

                    $.each(in_dis, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == fila_ter.id_distri) {
                                delete in_dis[index];
                            }
                        }

                    });

                    $.each(in_ter, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_terri == fila_ter.id) {
                                delete in_ter[index];
                            }
                        }

                    });

                    $.each(in_zonas, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_terri == fila_ter.id) {
                                delete in_zonas[index];
                            }
                        }

                    });

                    /*
                     delete in_ter[fila_ter.id_regional+fila_ter.id_distri+fila_ter.id];
                     delete in_dis[fila_ter.id_regional+fila_ter.id_distri];
                     delete in_regional[fila_ter.id_regional];*/

                }

            });

        }
    } else if (op == 3) {
        if ($("#all_zonas_" + id).hasClass("glyphicon-unchecked")) {

            $("#all_zonas_" + id).removeClass("glyphicon-unchecked");
            $("#all_zonas_" + id).addClass("glyphicon-check");

            $.each(datos_zonas, function (index, fila_zona) {

                if (fila_zona.territorio == id) {

                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-unchecked");
                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-check");

                    in_zonas.push({
                        id_reg: fila_zona.id_regional,
                        id_dis: fila_zona.id_distri,
                        id_terri: fila_zona.territorio,
                        id_zona: fila_zona.id,
                        op: "zonas"
                    });

                    //in_zonas[fila_zona.id_regional+fila_zona.id_distri+fila_zona.territorio+fila_zona.id] = {id_reg:fila_zona.id_regional, id_dis:fila_zona.id_distri, id_terri:fila_zona.territorio, id_zona:fila_zona.id,op:"zonas"};

                }

            });

        } else {

            $("#all_zonas_" + id).removeClass("glyphicon-check");
            $("#all_zonas_" + id).addClass("glyphicon-unchecked");

            $.each(datos_zonas, function (index, fila_zona) {

                if (fila_zona.territorio == id) {

                    $("#s_zona_" + fila_zona.id).removeClass("glyphicon-check");
                    $("#s_zona_" + fila_zona.id).addClass("glyphicon-unchecked");

                    if ($("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#all_ter_" + fila_zona.id_distri + "_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#s_dis_" + fila_zona.id_distri + "_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#all_dis_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#all_dis_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#all_dis_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    if ($("#s_reg_" + fila_zona.id_regional).hasClass("glyphicon-check")) {
                        $("#s_reg_" + fila_zona.id_regional).removeClass("glyphicon-check");
                        $("#s_reg_" + fila_zona.id_regional).addClass("glyphicon-unchecked");
                    }

                    $.each(in_regional, function (index, fila) {

                        if (fila !== undefined) {
                            if (fila.id_reg == fila_zona.id_regional) {
                                delete in_regional[index];
                            }
                        }

                    });

                    $.each(in_dis, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_dis == fila_zona.id_distri) {
                                delete in_dis[index];
                            }
                        }

                    });

                    $.each(in_ter, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_terri == fila_zona.territorio) {
                                delete in_ter[index];
                            }
                        }

                    });

                    $.each(in_zonas, function (index, fila) {
                        if (fila !== undefined) {
                            if (fila.id_zona == fila_zona.id) {
                                delete in_zonas[index];
                            }
                        }

                    });

                    /*

                     delete in_zonas[fila_zona.id_regional+fila_zona.id_distri+fila_zona.territorio+fila_zona.id];
                     delete in_ter[fila_zona.id_regional+fila_zona.id_distri+fila_zona.territorio];
                     delete in_dis[fila_zona.id_regional+fila_zona.id_distri];
                     delete in_regional[fila_zona.id_regional];*/

                }

            });

        }
    }
}

function limpiar_form(op) {
    if (op == 1) {
        $("#titulo").val("");
        $("#descrip_e").val("");
        $("#fecha_ini").val("");
        $("#fecha_fin").val("");

        vigencia_enc = 0;
        cant_pre = 0;
        cant_resp = 0;
        id_enc = 0;

        $("input:radio[name=c_obliga]:checked").prop("checked", false);
        $("input:radio[name=c_navega]:checked").prop("checked", false);
        $("input:radio[name=aplicar_encuesta]:checked").prop("checked", false);
        $("input:radio[name=reporte_distri]:checked").prop("checked", false);


        $("#titulo").prop("disabled", false);
        $("#descrip_e").prop("disabled", false);
        $("#fecha_ini").prop("disabled", false);
        $("input:radio[name=c_obliga]").prop("disabled", false);
        $("input:radio[name=c_navega]").prop("disabled", false);
        $("input:radio[name=aplicar_encuesta]").prop("disabled", false);
        $("input:radio[name=reporte_distri]:checked").prop("disabled", false);
    } else if (op == 2) {
        $("#pregunta").val("");
        $("#orden").val("").change();
        $("#tipopc").val("").change();

        $("input:radio[name=c_obliga]:checked").prop("checked", false);

        cant_resp = 0;
    } else {
        $("#respuesta").val("");
        $("#orden").val("").change();
    }
}

function ver_orden(order, op, condicion) {
    if (condicion == 1) {
        fetch("modulos/crear_preguntas_v1.0.0/controlador.php", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: "ver_orden",
                id_enc: id_enc,
                id_pregunta: id_pre,
                id_respuesta: id_resp
            })
        }).then(response => response.json()).then(data => {

                let orden = "<option value=''>Seleccionar...</option>";
                let selected = "";
                if (order == 0 && op > 0) {
                    selected = "selected";
                }
                orden += "<option value='0' " + selected + ">Al principio</option>";

                $.each(data, function (index, row) {

                    let selected = "";

                    if (order > 0 && order == row.orden) {
                        selected = "selected";
                    }

                    if (op > 0 && id_pre != row.id) {

                        orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.pregunta_enc + "</option>";

                    } else if (op == 0) {

                        orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.pregunta_enc + "</option>";

                    }

                });

                $("#orden_pregunta").html(orden).change();
        }).catch(error => {
            console.error('Error:', error);
        });
    } else {

        fetch("modulos/crear_respuestas_v1.0.0/controlador.php", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: "ver_orden",
                id_prel: id_pre
            })
        }).then(response => response.json()).then(data => {

                let orden = "<option value=''>Seleccionar...</option>";
                let selected = "";
                if (order == 0 && op > 0) {
                    selected = "selected";
                }
                orden += "<option value='0' " + selected + ">Al principio</option>";

                $.each(data, function (index, row) {

                    let selected = "";

                    if (order > 0 && order == row.orden) {
                        selected = "selected";
                    }

                    if (op > 0 && id_resp != row.id) {

                        orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.respuesta + "</option>";

                    } else if (op == 0) {

                        orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.respuesta + "</option>";

                    }

                });

                $("#orden_respuesta").html(orden).change();
        }).catch(error => {
            console.error('Error:', error);
        });
    }
}

function toHex(str) {
    let hex = "";

    for (let i = 0; i < str.length; i++) {
        hex += "" + str.charCodeAt(i).toString(16);
    }

    return hex;
}